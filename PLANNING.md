# Planning

## Project Overview

SUSE AIR is a Go application that converts OpenAPI v3 specifications into MCP (Model Context Protocol) tools and provides execution capabilities.
The project bridges OpenAPI specifications with MCP tool execution by storing conversion mappings in a PostgreSQL database.

## Key OpenAPI to MCP Conversion Challenges

Based on research from the field, several important considerations apply to this conversion process:

### Schema Transformation Complexity
- **Parameter Flattening**: OpenAPI endpoints combine request body, path parameters, query parameters, and headers into a single MCP tool schema
- **Root Object Requirement**: MCP tools require object-type root schemas, but OpenAPI can have primitive/array request bodies. The converter uses a "wrapped" strategy for non-object bodies
- **Naming Collision Handling**: When combining multiple parameter sources, automatic collision resolution is needed

### Reference Resolution Strategy
- **$ref Inlining**: The converter resolves `$ref` references from `#/components/schemas` into self-contained schemas
- **Circular Reference Detection**: Prevents infinite recursion during schema resolution with visited tracking
- **Schema Simplification**: Complex nested references are flattened to improve LLM comprehension

### Execution Context Considerations
- **Bidirectional Mapping**: The system maintains mappings to reconstruct original OpenAPI requests from MCP tool arguments
- **Parameter Distribution**: MCP arguments are correctly distributed back to path, query, header, and body locations
- **Request Body Reconstruction**: Supports both "wrapped" (single property) and "merged" (object composition) strategies

## Commands

### Building and Running
```bash
# Build the application
go build -o sair ./cmd/air

# Run convert command (converts OpenAPI spec to MCP tools)
./sair convert -file path/to/openapi.json -db "postgres://user:password@localhost:5432/mcpdb?sslmode=disable"

# Run execute command (executes an MCP tool via API call)
./sair execute -tool toolName -args '{"key":"value"}' -base-url "https://api.example.com" -db "postgres://user:password@localhost:5432/mcpdb?sslmode=disable"
```

### Database Operations
```bash
# Generate Go code from SQL using sqlc
sqlc generate

# Run database migrations (manual - use your preferred migration tool)
# Migrations are in sql/migrations/
```

### Dependencies
```bash
# Install/update dependencies
go mod tidy
go mod download
```

## Architecture

### Core Components

- **cmd/air/main.go**: Main entry point with CLI flag parsing and command dispatch
- **internal/schema/converter.go**: Converts OpenAPI specs to MCP tools with mapping generation
- **internal/schema/executor.go**: Rebuilds HTTP requests from MCP tool arguments using stored mappings
- **internal/db/**: Generated database layer using sqlc for PostgreSQL operations
- **internal/models/**: Data structures for OpenAPI, MCP tools, and conversion mappings

### Key Data Flow

1. **Convert Mode**: OpenAPI JSON → Schema conversion → Database storage (tools + mappings)
2. **Execute Mode**: Database lookup → Request reconstruction → HTTP API execution

### Database Schema

Two main tables managed via PostgreSQL:
- `mcp_tools`: Stores generated MCP tool definitions with JSON schemas
- `mcp_tool_mappings`: Stores the conversion mappings to reconstruct original OpenAPI requests

### Schema Resolution

The converter handles OpenAPI `$ref` resolution with circular reference detection, inlining referenced schemas from `#/components/schemas`.

### Request Reconstruction

Two body mapping strategies:
- **Wrapped**: Single MCP property contains entire request body
- **Merged**: Multiple MCP properties compose the request body object

### Tool Generation

Uses `github.com/iancoleman/strcase` for consistent camelCase tool naming from OpenAPI operationId or generated names.

## Testing

### Running Tests
```bash
# All tests (requires Docker for integration tests)
make test

# Unit tests only (no external dependencies)
make test-unit

# Integration tests only (requires Docker or TEST_DATABASE_URL)
make test-integration

# Tests with coverage report
make test-coverage

# Skip database tests entirely
make test-no-docker
```

### Test Structure
- **Test Readme**: `tests/README.md` - Detailed instructions and test categories
- **Unit Tests**: `tests/unit/` - Test individual components without external dependencies
- **Integration Tests**: `tests/integration/` - Test with real PostgreSQL database using Docker containers
- **Test Data**: `tests/testdata/` - Sample OpenAPI specifications for testing conversion logic

### Database Testing
Integration tests use dockertest to automatically create PostgreSQL containers. Set `TEST_DATABASE_URL` to use an existing database or `SKIP_DB_TESTS=true` to skip database tests entirely.

