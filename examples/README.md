# Examples

### Alt Screen Toggle

The `altscreen-toggle` example shows how to transition between the alternative
screen buffer and the normal screen buffer using Bubble Tea.

<a href="./altscreen-toggle/main.go">
  <img width="750" src="./altscreen-toggle/altscreen-toggle.gif" />
</a>

### Chat

The `chat` examples shows a basic chat application with a multi-line `textarea`
input.

<a href="./chat/main.go">
  <img width="750" src="./chat/chat.gif" />
</a>

### Composable Views

The `composable-views` example shows how to compose two bubble models (spinner
and timer) together in a single application and switch between them.

<a href="./composable-views/main.go">
  <img width="750" src="./composable-views/composable-views.gif" />
</a>

### Credit Card Form

The `credit-card-form` example demonstrates how to build a multi-step form with
`textinputs` bubbles and validation on the inputs.

<a href="./credit-card-form/main.go">
  <img width="750" src="./credit-card-form/credit-card-form.gif" />
</a>

### Debounce

The `debounce` example shows how to throttle key presses to avoid overloading
your Bubble Tea application.

<a href="./debounce/main.go">
  <img width="750" src="./debounce/debounce.gif" />
</a>

### Exec

The `exec` example shows how to execute a running command during the execution
of a Bubble Tea application such as launching an `EDITOR`.
 
<a href="./exec/main.go">
  <img width="750" src="./exec/exec.gif" />
</a>

### Full Screen

The `fullscreen` example shows how to make a Bubble Tea application fullscreen.

<a href="./fullscreen/main.go">
  <img width="750" src="./fullscreen/fullscreen.gif" />
</a>

### Glamour

The `glamour` example shows how to use [Glamour](https://github.com/charmbracelet/glamour) inside a viewport bubble.

<a href="./glamour/main.go">
  <img width="750" src="./glamour/glamour.gif" />
</a>

### Help

The `help` example shows how to use the `help` bubble to display help to the
user of your application.

<a href="./help/main.go">
  <img width="750" src="./help/help.gif" />
</a>

### Http

The `http` example shows how to make an `http` call within your Bubble Tea
application.

<a href="./http/main.go">
  <img width="750" src="./http/http.gif" />
</a>

### Default List

The `list-default` example shows how to use the list bubble.

<a href="./list-default/main.go">
  <img width="750" src="./list-default/list-default.gif" />
</a>

### Fancy List

The `list-fancy` example shows how to use the list bubble with extra customizations.

<a href="./list-fancy/main.go">
  <img width="750" src="./list-fancy/list-fancy.gif" />
</a>

### Simple List

The `list-simple` example shows how to use the list and customize it to have a simpler, more compact, appearance.

<a href="./list-simple/main.go">
  <img width="750" src="./list-simple/list-simple.gif" />
</a>

### Mouse

The `mouse` example shows how to receive mouse events in a Bubble Tea
application.

<a href="./mouse/main.go">
  Code
</a>

### Package Manager

The `package-manager` example shows how to build an interface for a package
manager using the `tea.Println` feature.

<a href="./package-manager/main.go">
  <img width="750" src="./package-manager/package-manager.gif" />
</a>

### Pager

The `pager` example shows how to build a simple pager application similar to
`less`.

<a href="./pager/main.go">
  <img width="750" src="./pager/pager.gif" />
</a>

### Paginator

The `paginator` example shows how to build a simple paginated list.

<a href="./paginator/main.go">
  <img width="750" src="./paginator/paginator.gif" />
</a>

### Pipe

The `pipe` example demonstrates using shell pipes to communicate with Bubble
Tea applications.

<a href="./pipe/main.go">
  <img width="750" src="./pipe/pipe.gif" />
</a>

### Animated Progress

The `progress-animated` example shows how to build a progress bar with an
animated progression.

<a href="./progress-animated/main.go">
  <img width="750" src="./progress-animated/progress-animated.gif" />
</a>

### Download Progress

The `progress-download` example demonstrates how to download a file while
indicating download progress through Bubble Tea.

<a href="./progress-download/main.go">
  Code
</a>

### Static Progress

The `progress-static` example shows a progress bar with static incrementation
of progress.

<a href="./progress-static/main.go">
  <img width="750" src="./progress-static/progress-static.gif" />
</a>

### Real Time

The `realtime` example demonstrates the use of go channels to perform realtime
communication with a Bubble Tea application.

<a href="./realtime/main.go">
  <img width="750" src="./realtime/realtime.gif" />
</a>

### Result

The `result` example shows a choice menu with the ability to select an option.

<a href="./result/main.go">
  <img width="750" src="./result/result.gif" />
</a>

### Send Msg

The `send-msg` example demonstrates the usage of custom `tea.Msg`s.

<a href="./send-msg/main.go">
  <img width="750" src="./send-msg/send-msg.gif" />
</a>

### Sequence

The `sequence` example demonstrates the `tea.Sequence` command.

<a href="./sequence/main.go">
  <img width="750" src="./sequence/sequence.gif" />
</a>

### Simple

The `simple` example shows a very simple Bubble Tea application.

<a href="./simple/main.go">
  <img width="750" src="./simple/simple.gif" />
</a>

### Spinner

The `spinner` example demonstrates a spinner bubble being used to indicate loading.

<a href="./spinner/main.go">
  <img width="750" src="./spinner/spinner.gif" />
</a>

### Spinners

The `spinner` example shows various spinner types that are available.

<a href="./spinners/main.go">
  <img width="750" src="./spinners/spinners.gif" />
</a>

### Split Editors

The `split-editors` example shows multiple `textarea`s being used in a single
application and being able to switch focus between them.

<a href="./split-editors/main.go">
  <img width="750" src="./split-editors/split-editors.gif" />
</a>

### Stop Watch

The `stopwatch` example shows a sample stop watch built with Bubble Tea.

<a href="./stopwatch/main.go">
  <img width="750" src="./stopwatch/stopwatch.gif" />
</a>

### Table

The `table` example demonstrates the table bubble being used to display tabular
data.

<a href="./table/main.go">
  <img width="750" src="./table/table.gif" />
</a>

### Tabs

The `tabs` example demonstrates tabbed navigation styled with [Lip Gloss](https://github.com/charmbracelet/lipgloss).

<a href="./tabs/main.go">
  <img width="750" src="./tabs/tabs.gif" />
</a>

### Text Area

The `textarea` example demonstrates a simple Bubble Tea application using a
`textarea` bubble.

<a href="./textarea/main.go">
  <img width="750" src="./textarea/textarea.gif" />
</a>

### Text Input

The `textinput` example demonstrates a simple Bubble Tea application using a `textinput` bubble.

<a href="./textinput/main.go">
  <img width="750" src="./textinput/textinput.gif" />
</a>

### Multiple Text Inputs

The `textinputs` example shows multiple `textinputs` and being able to switch
focus between them as well as changing the cursor mode.

<a href="./textinputs/main.go">
  <img width="750" src="./textinputs/textinputs.gif" />
</a>

### Timer

The `timer` example shows a simple timer built with Bubble Tea.

<a href="./timer/main.go">
  <img width="750" src="./timer/timer.gif" />
</a>

### TUI Daemon

The `tui-daemon-combo` demonstrates building a text-user interface along with a
daemon mode using Bubble Tea.

<a href="./tui-daemon-combo/main.go">
  <img width="750" src="./tui-daemon-combo/tui-daemon-combo.gif" />
</a>

### Views

The `views` example demonstrates how to build a Bubble Tea application with
multiple views and switch between them.

<a href="./views/main.go">
  <img width="750" src="./views/views.gif" />
</a>

