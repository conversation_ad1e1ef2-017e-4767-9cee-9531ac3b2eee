package server

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

var jwtKey = []byte("your_secret_key") // TODO: Replace with a strong, secure key from config

// <PERSON><PERSON><PERSON> defines the JWT claims for the access token.
type Claims struct {
	Audience  string `json:"aud"`
	jwt.RegisteredClaims
}

// GenerateTestToken generates a signed JWT token for testing purposes.
func GenerateTestToken(subject, profile string, port int) (string, error) {
	audience := fmt.Sprintf("localhost:%d/mcp/%s", port, profile)

	expirationTime := time.Now().Add(5 * time.Minute)
	claims := &Claims{
		Audience: audience,
		RegisteredClaims: jwt.RegisteredClaims{
			Subject:   subject,
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "test-issuer",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(jwtKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}
	return tokenString, nil
}

// AuthMiddleware is a middleware that checks for a valid access token.
func AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			Unauthorized(w, r)
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || strings.ToLower(parts[0]) != "bearer" {
			Unauthorized(w, r)
			return
		}

		tokenString := parts[1]

		claims := &Claims{}

		token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
			return jwtKey, nil
		})

		if err != nil {
			if err == jwt.ErrSignatureInvalid {
				w.WriteHeader(http.StatusUnauthorized)
				return
			}
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		if !token.Valid {
			w.WriteHeader(http.StatusUnauthorized)
			return
		}

		// Validate the audience claim according to RFC 8707 Section 2
		// MCP servers MUST validate that access tokens were issued specifically for them as the intended audience
		profile, ok := r.Context().Value("profile").(string)
		log.Printf("AuthMiddleware: Retrieved profile from context: %v, ok: %t", profile, ok)
		if !ok {
			log.Printf("AuthMiddleware: Profile not found in context for host %s", r.Host)
			Unauthorized(w, r)
			return
		}

		// Construct expected audience: host + /mcp/ + profile
		expectedAudience := r.Host + "/mcp/" + profile

		log.Printf("AuthMiddleware: r.Host=%s, profile=%s, claims.Audience=%s, expectedAudience=%s", r.Host, profile, claims.Audience, expectedAudience)

		// Validate audience claim - must match exactly
		if claims.Audience != expectedAudience {
			log.Printf("AuthMiddleware: Audience validation failed. Expected: %s, Got: %s", expectedAudience, claims.Audience)
			w.WriteHeader(http.StatusUnauthorized)
			return
		}

		log.Printf("AuthMiddleware: Audience validation successful for profile: %s", profile)

		// Add user information to the context
		ctx := context.WithValue(r.Context(), "user", claims.Subject)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// Unauthorized sends a 401 Unauthorized response with the WWW-Authenticate header.
func Unauthorized(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("WWW-Authenticate", fmt.Sprintf("Bearer realm=\"%s\"", r.Host))
	http.Error(w, "Unauthorized", http.StatusUnauthorized)
}

// ProtectedResourceMetadataHandler handles requests for the OAuth 2.0 Protected Resource Metadata.
func ProtectedResourceMetadataHandler(w http.ResponseWriter, r *http.Request) {
	metadata := map[string]interface{}{
		"authorization_servers": []string{
			"/.well-known/oauth-authorization-server",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(metadata); err != nil {
		// Handle error
	}
}
