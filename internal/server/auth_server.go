package server

import (
	"encoding/json"
	"net/http"
	"net/url"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/oauth2"
)

// AuthorizationServerMetadataHandler handles requests for the OAuth 2.0 Authorization Server Metadata.
func AuthorizationServerMetadataHandler(w http.ResponseWriter, r *http.Request) {
	metadata := map[string]interface{}{
		"issuer":                                 "http://" + r.Host,
		"authorization_endpoint":                 "http://" + r.Host + "/authorize",
		"token_endpoint":                         "http://" + r.Host + "/token",
		"code_challenge_methods_supported":       []string{"S256"},
		"response_types_supported":               []string{"code"},
		"grant_types_supported":                  []string{"authorization_code"},
		"token_endpoint_auth_methods_supported": []string{"none"}, // For public clients
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(metadata); err != nil {
		// Handle error
	}
}

// AuthorizeHandler handles authorization requests.
func AuthorizeHandler(w http.ResponseWriter, r *http.Request) {
	// This is a simplified flow. In a real application, you would:
	// 1. Authenticate the user.
	// 2. Ask the user to grant consent.
	// 3. Generate an authorization code and store it with the request details.

	q := r.URL.Query()
	redirectURI := q.Get("redirect_uri")
	if redirectURI == "" {
		http.Error(w, "redirect_uri is required", http.StatusBadRequest)
		return
	}

	// For this example, we'll just generate a dummy code and redirect back.
	code := "dummy-auth-code"
	state := q.Get("state")

	redirectURL, _ := url.Parse(redirectURI)
	newQuery := redirectURL.Query()
	newQuery.Set("code", code)
	if state != "" {
		newQuery.Set("state", state)
	}
	redirectURL.RawQuery = newQuery.Encode()

	http.Redirect(w, r, redirectURL.String(), http.StatusFound)
}

// TokenHandler handles token requests.
func TokenHandler(w http.ResponseWriter, r *http.Request) {
	// This is a simplified flow. In a real application, you would:
	// 1. Validate the authorization code.
	// 2. Verify the PKCE code_verifier.

	if err := r.ParseForm(); err != nil {
		http.Error(w, "Failed to parse form", http.StatusBadRequest)
		return
	}

	code := r.FormValue("code")
	if code != "dummy-auth-code" {
		http.Error(w, "Invalid authorization code", http.StatusBadRequest)
		return
	}

	// Create the JWT claims, which includes the audience.
	expirationTime := time.Now().Add(5 * time.Minute)
	claims := &Claims{
		Audience: "http://" + r.Host, // The audience is the MCP server itself.
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
		},
	}

	// Create the token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(jwtKey)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		return
	}

	// Dummy token response
	oauthToken := &oauth2.Token{
		AccessToken:  tokenString,
		TokenType:    "Bearer",
		RefreshToken: "dummy-refresh-token",
		Expiry:       expirationTime,
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(oauthToken); err != nil {
		// Handle error
	}
}