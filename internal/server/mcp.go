package server

import (
	"context"
	"encoding/json"
	"io"
	"log"
	"net/http"
	"strings"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/schema"
	"github.com/ravan/suse-air/internal/util"
)

// MCP-related constants
const (
	ProtocolVersion = "2025-03-26"
	JSONRPCVersion  = "2.0"
)

// JSONRPCRequest represents a JSON-RPC request
type JSONRPCRequest struct {
	JSONRPC string          `json:"jsonrpc"`
	ID      interface{}     `json:"id"`
	Method  string          `json:"method"`
	Params  json.RawMessage `json:"params"`
}

// JSONRPCResponse represents a JSON-RPC response
type JSONRPCResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	ID      interface{} `json:"id"`
	Result  interface{} `json:"result,omitempty"`
	Error   *RPCError   `json:"error,omitempty"`
}

// RPCError represents a JSON-RPC error object
type RPCError struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// Server is the main MCP server struct
type Server struct {
	DB      db.Querier
	BaseURL string
}

// NewServer creates a new MCP server
func NewServer(db db.Querier) *Server {
	return &Server{DB: db, BaseURL: "http://localhost:8081"} // Dummy URL for now
}

// ServeHTTP handles incoming HTTP requests
func (s *Server) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Handle .well-known paths based on the full URL path
	if strings.Contains(r.URL.Path, ".well-known") {
		if strings.Contains(r.URL.Path, "oauth-protected-resource") {
			ProtectedResourceMetadataHandler(w, r)
			return
		} else if strings.Contains(r.URL.Path, "oauth-authorization-server") {
			AuthorizationServerMetadataHandler(w, r)
			return
		}
	}

	body, err := io.ReadAll(r.Body)
	log.Printf("ServeHTTP: Received request: %s", string(body))
	if err != nil {
		// Handle error
		return
	}

	var req JSONRPCRequest
	if err := json.Unmarshal(body, &req); err != nil {
		// Handle error
		return
	}

	switch req.Method {
	case "initialize":
		s.handleInitialize(w, req, r)
	case "tools/list":
		s.handleToolsList(w, req, r)
	case "tools/call":
		s.handleToolCall(w, req, r)
	default:
		// Handle unknown methods
	}
}

func (s *Server) handleInitialize(w http.ResponseWriter, req JSONRPCRequest, r *http.Request) {
	// For now, a simple response
	resp := JSONRPCResponse{
		JSONRPC: JSONRPCVersion,
		ID:      req.ID,
		Result: map[string]interface{}{
			"protocolVersion": ProtocolVersion,
			"capabilities": map[string]interface{}{
				"tools": map[string]interface{}{
					"listChanged": true,
				},
			},
			"serverInfo": map[string]string{
				"name":    "suse-air",
				"version": "0.0.1",
			},
		},
	}

	w.Header().Set("Content-Type", "application/json")
	rb, err := json.Marshal(resp)
	if err != nil {
		return
	}
	log.Printf("handleInitialize: Sending response: %+v", string(rb))
	if err := json.NewEncoder(w).Encode(resp); err != nil {
		log.Printf("handleToolsList: Error encoding response: %v", err)
		// Handle error
	}
}

// ListToolsResult represents the result of a tools/list request
type ListToolsResult struct {
	Tools []Tool `json:"tools"`
}

// Tool represents an MCP tool
type Tool struct {
	Name        string      `json:"name"`
	Description string      `json:"description,omitempty"`
	InputSchema interface{} `json:"inputSchema"`
}

func (s *Server) handleToolsList(w http.ResponseWriter, req JSONRPCRequest, r *http.Request) {
	pathSegment, ok := r.Context().Value("profile").(string)
	if !ok {
		log.Printf("handleToolsList: Profile path segment not found in context for request: %+v", req)
		// Handle error: profile not found in context
		return
	}

	ctx := r.Context()

	// First, get the profile by path segment
	profile, err := s.DB.GetProfileByPathSegment(ctx, pathSegment)
	if err != nil {
		log.Printf("handleToolsList: Error getting profile by path segment %s: %v", pathSegment, err)
		// Handle error
		return
	}

	// Then get the tools for that profile by name
	dbTools, err := s.DB.ListMCPToolsByProfile(ctx, profile.Name)
	if err != nil {
		log.Printf("handleToolsList: Error listing tools for profile %s (path: %s): %v", profile.Name, pathSegment, err)
		// Handle error
		return
	}

	log.Printf("handleToolsList: Retrieved %d tools for profile %s (path: %s)", len(dbTools), profile.Name, pathSegment)

	tools := make([]Tool, len(dbTools))
	for i, dbTool := range dbTools {
		var inputSchema interface{}
		if err := json.Unmarshal(dbTool.InputSchema, &inputSchema); err != nil {
			log.Printf("handleToolsList: Error unmarshaling input schema for tool %s: %v", dbTool.ToolName, err)
			// Handle error
			continue
		}

		// Convert types for JSON schema compatibility
		convertedSchema, err := util.ConvertJSONBTypes(inputSchema)
		if err != nil {
			log.Printf("handleToolsList: Error converting JSONB types for tool %s: %v", dbTool.ToolName, err)
			// Handle error
			continue
		}

		tools[i] = Tool{
			Name:        dbTool.ToolName,
			Description: dbTool.Description.String,
			InputSchema: convertedSchema,
		}
	}

	resp := JSONRPCResponse{
		JSONRPC: JSONRPCVersion,
		ID:      req.ID,
		Result:  ListToolsResult{Tools: tools},
	}

	log.Printf("handleToolsList: Sending response: %+v", resp)

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(resp); err != nil {
		log.Printf("handleToolsList: Error encoding response: %v", err)
		// Handle error
	}
}

// CallToolResult represents the result of a tools/call request
type CallToolResult struct {
	Content []interface{} `json:"content"`
	IsError bool          `json:"isError,omitempty"`
}

func (s *Server) handleToolCall(w http.ResponseWriter, req JSONRPCRequest, r *http.Request) {
	ctx := context.Background() // In a real app, you'd get this from the request

	var params struct {
		Name      string                 `json:"name"`
		Arguments map[string]interface{} `json:"arguments"`
	}
	if err := json.Unmarshal(req.Params, &params); err != nil {
		log.Printf("handleToolCall: Error unmarshaling request params: %v", err)
		// Handle error
		return
	}

	log.Printf("handleToolCall: Received tool call for tool %s with arguments %+v", params.Name, params.Arguments)

	toolWithMapping, err := s.DB.GetToolWithMapping(ctx, params.Name)
	if err != nil {
		log.Printf("handleToolCall: Error fetching tool with mapping for %s: %v", params.Name, err)
		// Handle error
		return
	}

	log.Printf("handleToolCall: Fetched tool with mapping: %+v", toolWithMapping)

	httpReq, err := schema.BuildRequest(toolWithMapping, params.Arguments, s.BaseURL)
	if err != nil {
		log.Printf("handleToolCall: Error building HTTP request: %v", err)
		// Handle error
		return
	}

	log.Printf("handleToolCall: Built HTTP request: %s %s", httpReq.Method, httpReq.URL.String())

	client := schema.NewAPIClient()
	httpResp, err := client.Do(httpReq)
	if err != nil {
		log.Printf("handleToolCall: Error executing API request: %v", err)
		// Handle error
		return
	}
	defer httpResp.Body.Close()

	log.Printf("handleToolCall: API Response Status: %s", httpResp.Status)

	body, err := io.ReadAll(httpResp.Body)
	if err != nil {
		log.Printf("handleToolCall: Error reading API response body: %v", err)
		// Handle error
		return
	}

	log.Printf("handleToolCall: API Response Body: %s", string(body))

	resp := JSONRPCResponse{
		JSONRPC: JSONRPCVersion,
		ID:      req.ID,
		Result: CallToolResult{
			Content: []interface{}{
				map[string]string{"type": "text", "text": string(body)},
			},
		},
	}

	log.Printf("handleToolCall: Sending response: %+v", resp)

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(resp); err != nil {
		log.Printf("handleToolCall: Error encoding response: %v", err)
		// Handle error
	}
}
