package schema

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/iancoleman/strcase"
	"github.com/ravan/suse-air/internal/models"
)

// ProcessOpenAPISpec converts a full OpenAPI spec into a slice of combined tool/mapping results.
func ProcessOpenAPISpec(spec *models.OpenAPISpec) ([]models.ConversionResult, error) {
	var results []models.ConversionResult

	for path, pathItem := range spec.Paths {
		for method, operation := range pathItem {
			toolName := operation.OperationID
			if toolName == "" {
				toolName = generateToolName(method, path)
			}
			toolName = strcase.ToCamel(toolName)

			description := operation.Summary
			if description == "" {
				description = operation.Description
			}

			// Initialize containers for the conversion
			toolProperties := make(map[string]interface{})
			requiredFields := []string{}
			paramMappings := make(map[string]models.ParamMapping)
			bodyMapping := models.BodyMapping{SourceProperties: []string{}}

			// 1. Process path, query, and header parameters
			for _, param := range operation.Parameters {
				if param.Schema != nil {
					if _, ok := param.Schema["description"]; !ok && param.Description != "" {
						param.Schema["description"] = param.Description
					}
					toolProperties[param.Name] = param.Schema
					paramMappings[param.Name] = models.ParamMapping{
						Source:     param.In,
						SourceName: param.Name,
					}
					if param.Required {
						requiredFields = append(requiredFields, param.Name)
					}
				}
			}

			// 2. Process request body
			if content, ok := operation.RequestBody.Content["application/json"]; ok {
				bodySchema := content.Schema
				if schemaType, ok := bodySchema["type"].(string); !ok || schemaType != "object" {
					// Strategy: Wrapped. The body is a primitive or array.
					bodyMapping.Strategy = "wrapped"
					bodyMapping.WrappedProperty = "body"
					toolProperties["body"] = bodySchema
					if operation.RequestBody.Required {
						requiredFields = append(requiredFields, "body")
					}
				} else {
					// Strategy: Merged. The body is an object.
					bodyMapping.Strategy = "merged"
					if props, ok := bodySchema["properties"].(map[string]interface{}); ok {
						for k, v := range props {
							toolProperties[k] = v
							bodyMapping.SourceProperties = append(bodyMapping.SourceProperties, k)
						}
					}
					if reqs, ok := bodySchema["required"].([]interface{}); ok {
						for _, req := range reqs {
							if reqStr, ok := req.(string); ok {
								requiredFields = append(requiredFields, reqStr)
							}
						}
					}
				}
			}

			// 3. Resolve $refs
			finalProperties := make(map[string]interface{})
			for name, propSchema := range toolProperties {
				var schemaMap map[string]interface{}
				
				// Handle both map[string]interface{} and models.SchemaDef types
				switch s := propSchema.(type) {
				case map[string]interface{}:
					schemaMap = s
				case models.SchemaDef:
					schemaMap = map[string]interface{}(s)
				default:
					fmt.Printf("Warning: Unexpected schema type for property '%s' in tool '%s'. Skipping.\n", name, toolName)
					continue
				}
				
				resolvedSchema, err := resolveRefs(schemaMap, spec, make(map[string]bool))
				if err != nil {
					fmt.Printf("Warning: Could not resolve schema for property '%s' in tool '%s': %v. Skipping.\n", name, toolName, err)
					continue
				}
				finalProperties[name] = resolvedSchema
			}

			// 4. Assemble the final tool and mapping
			mcpTool := models.Tool{
				Name:        toolName,
				Description: description,
				InputSchema: map[string]interface{}{
					"type":       "object",
					"properties": finalProperties,
				},
			}
			if len(requiredFields) > 0 {
				mcpTool.InputSchema["required"] = requiredFields
			}

			mcpMapping := models.MCPToolMapping{
				OpenAPIPath:   path,
				HTTPMethod:    strings.ToUpper(method),
				ParamMappings: paramMappings,
				BodyMapping:   bodyMapping,
			}

			results = append(results, models.ConversionResult{Tool: mcpTool, Mapping: mcpMapping})
		}
	}
	return results, nil
}

// resolveRefs recursively traverses a schema, inlining any $ref it finds.
func resolveRefs(schema map[string]interface{}, spec *models.OpenAPISpec, visited map[string]bool) (map[string]interface{}, error) {
	resolvedSchema := make(map[string]interface{})

	for key, value := range schema {
		if key == "$ref" {
			refPath, ok := value.(string)
			if !ok {
				return nil, fmt.Errorf("invalid $ref format: not a string")
			}

			if visited[refPath] {
				fmt.Printf("Warning: Circular reference detected at '%s'. Dropping property.\n", refPath)
				return make(map[string]interface{}), nil
			}
			visited[refPath] = true

			refSchema, err := findRefSchema(refPath, spec)
			if err != nil {
				return nil, err
			}

			newVisited := make(map[string]bool)
			for k, v := range visited {
				newVisited[k] = v
			}

			return resolveRefs(refSchema, spec, newVisited)
		}

		switch v := value.(type) {
		case map[string]interface{}:
			var err error
			resolvedSchema[key], err = resolveRefs(v, spec, visited)
			if err != nil {
				return nil, err
			}
		case []interface{}:
			var err error
			resolvedSchema[key], err = resolveRefsInArray(v, spec, visited)
			if err != nil {
				return nil, err
			}
		default:
			resolvedSchema[key] = value
		}
	}
	return resolvedSchema, nil
}

// resolveRefsInArray handles recursion for arrays of schemas.
func resolveRefsInArray(arr []interface{}, spec *models.OpenAPISpec, visited map[string]bool) ([]interface{}, error) {
	resolvedArr := make([]interface{}, len(arr))
	for i, item := range arr {
		if itemMap, ok := item.(map[string]interface{}); ok {
			var err error
			resolvedArr[i], err = resolveRefs(itemMap, spec, visited)
			if err != nil {
				return nil, err
			}
		} else {
			resolvedArr[i] = item
		}
	}
	return resolvedArr, nil
}

// findRefSchema navigates the spec to find the schema pointed to by a $ref path.
func findRefSchema(refPath string, spec *models.OpenAPISpec) (map[string]interface{}, error) {
	if !strings.HasPrefix(refPath, "#/components/") {
		return nil, fmt.Errorf("unsupported $ref path: must start with '#/components/'")
	}

	parts := strings.Split(strings.TrimPrefix(refPath, "#/"), "/")
	var current interface{} = spec.Components
	for _, part := range parts[1:] {
		if currentMap, ok := current.(map[string]interface{}); ok {
			if next, exists := currentMap[part]; exists {
				current = next
			} else {
				return nil, fmt.Errorf("ref path part '%s' not found", part)
			}
		} else {
			return nil, fmt.Errorf("invalid structure in components at path part '%s'", part)
		}
	}

	if finalSchema, ok := current.(map[string]interface{}); ok {
		return finalSchema, nil
	}
	return nil, fmt.Errorf("resolved ref '%s' is not a valid schema object", refPath)
}

var invalidChars = regexp.MustCompile(`[^a-zA-Z0-9_]`)

// generateToolName creates a fallback tool name from the HTTP method and path.
func generateToolName(method, path string) string {
	path = invalidChars.ReplaceAllString(path, "_")
	path = strings.Replace(path, "_", " ", -1)
	path = strings.Title(path)
	path = strings.Replace(path, " ", "", -1)
	return fmt.Sprintf("%s%s", strings.ToLower(method), path)
}
