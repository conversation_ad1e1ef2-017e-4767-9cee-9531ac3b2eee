package schema

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/models"
	"github.com/ravan/suse-air/internal/util"
)

// APIClient is a simple wrapper around http.Client.
type APIClient struct {
	client *http.Client
}

// NewAPIClient creates a new API client.
func NewAPIClient() *APIClient {
	return &APIClient{
		client: &http.Client{},
	}
}

// Do executes an http.Request.
func (c *APIClient) Do(req *http.Request) (*http.Response, error) {
	return c.client.Do(req)
}

// BuildRequest reconstructs an http.Request from the stored mapping and provided arguments.
func BuildRequest(
	toolWithMapping db.GetToolWithMappingRow,
	mcpArgs map[string]interface{},
	baseURL string,
) (*http.Request, error) {

	// 1. Unmarshal the JSONB mapping fields from the database result
	var paramMappings map[string]models.ParamMapping
	if err := json.Unmarshal(toolWithMapping.ParamMappings, &paramMappings); err != nil {
		return nil, fmt.Errorf("could not unmarshal param mappings: %w", err)
	}
	// Convert types for JSON schema compatibility
	convertedParamMappings, err := util.ConvertJSONBTypes(paramMappings)
	if err != nil {
		return nil, fmt.Errorf("could not convert param mappings: %w", err)
	}
	paramMappings = convertedParamMappings.(map[string]models.ParamMapping)

	var bodyMapping models.BodyMapping
	if err := json.Unmarshal(toolWithMapping.BodyMapping, &bodyMapping); err != nil {
		return nil, fmt.Errorf("could not unmarshal body mapping: %w", err)
	}

	// 2. Initialize containers for the reconstructed request parts
	pathParams := make(map[string]string)
	queryParams := url.Values{}
	headerParams := http.Header{}
	var requestBody io.Reader

	// 3. Distribute MCP arguments to their original OpenAPI locations
	for mcpKey, mcpValue := range mcpArgs {
		// Check if it's a parameter (path, query, header)
		if pMap, ok := paramMappings[mcpKey]; ok {
			strValue := fmt.Sprintf("%v", mcpValue)
			switch pMap.Source {
			case "path":
				pathParams[pMap.SourceName] = strValue
			case "query":
				queryParams.Add(pMap.SourceName, strValue)
			case "header":
				headerParams.Add(pMap.SourceName, strValue)
			}
		}
	}

	// 4. Reconstruct the request body
	if bodyMapping.Strategy != "" {
		switch bodyMapping.Strategy {
		case "wrapped":
			// The entire request body is the value of a single MCP property
			if bodyVal, ok := mcpArgs[bodyMapping.WrappedProperty]; ok {
				log.Printf("BuildRequest: Wrapped strategy - bodyVal: %+v, WrappedProperty: %s", bodyVal, bodyMapping.WrappedProperty)
				bodyBytes, err := json.Marshal(bodyVal)
				if err != nil {
					return nil, fmt.Errorf("failed to marshal wrapped request body: %w", err)
				}
				log.Printf("BuildRequest: Wrapped strategy - marshaled bodyBytes: %s", string(bodyBytes))
				requestBody = bytes.NewBuffer(bodyBytes)
			} else {
				log.Printf("BuildRequest: Wrapped strategy - WrappedProperty %s not found in mcpArgs", bodyMapping.WrappedProperty)
			}
		case "merged":
			// The request body is an object composed of multiple MCP properties
			bodyObject := make(map[string]interface{})
			for _, propName := range bodyMapping.SourceProperties {
				if val, ok := mcpArgs[propName]; ok {
					bodyObject[propName] = val
				}
			}
			if len(bodyObject) > 0 {
				bodyBytes, err := json.Marshal(bodyObject)
				if err != nil {
					return nil, fmt.Errorf("failed to marshal merged request body: %w", err)
				}
				requestBody = bytes.NewBuffer(bodyBytes)
			}
		}
	}

	// 5. Build the final URL
	// Replace path parameters like {userId} with their values
	finalPath := toolWithMapping.OpenapiPath
	for pName, pValue := range pathParams {
		finalPath = strings.ReplaceAll(finalPath, "{"+pName+"}", pValue)
	}

	fullURL, err := url.Parse(strings.TrimRight(baseURL, "/") + finalPath)
	if err != nil {
		return nil, fmt.Errorf("failed to parse URL: %w", err)
	}
	if len(queryParams) > 0 {
		fullURL.RawQuery = queryParams.Encode()
	}

	// 6. Create the http.Request object
	req, err := http.NewRequest(toolWithMapping.HttpMethod, fullURL.String(), requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create new http request: %w", err)
	}

	// Add headers
	req.Header = headerParams
	if requestBody != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return req, nil
}
