package util

import (
	"fmt"
)

// ConvertJSONBTypes recursively converts specific JSONB array types from []interface{} to []string.
// This is necessary because json.Unmarshal unmarshals JSON arrays into []interface{},
// but sqlc-generated code and some tests expect []string for 'required' and 'enum' fields.
func ConvertJSONBTypes(data interface{}) (interface{}, error) {
	switch v := data.(type) {
	case map[string]interface{}:
		for key, value := range v {
			if key == "required" || key == "enum" {
				if arr, ok := value.([]interface{}); ok {
					strArr := make([]string, len(arr))
					for i, item := range arr {
						if s, ok := item.(string); ok {
							strArr[i] = s
						} else {
							return nil, fmt.Errorf("expected string in array for key %s, got %T", key, item)
						}
					}
					v[key] = strArr
				} else if value != nil {
					return nil, fmt.<PERSON><PERSON><PERSON>("expected array for key %s, got %T", key, value)
				}
			} else {
				convertedValue, err := ConvertJSONBTypes(value)
				if err != nil {
					return nil, err
				}
				v[key] = convertedValue
			}
		}
	case []interface{}:
		for i, item := range v {
			convertedItem, err := ConvertJSONBTypes(item)
			if err != nil {
				return nil, err
			}
			v[i] = convertedItem
		}
	}
	return data, nil
}
