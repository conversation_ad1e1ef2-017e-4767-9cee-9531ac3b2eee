package models

// Tool represents a single MCP Tool.
type Tool struct {
	Name        string                 `json:"name"`
	Description string                 `json="description"`
	InputSchema map[string]interface{} `json:"inputSchema"`
}

// ConversionResult holds a generated tool and its corresponding mapping.
type ConversionResult struct {
	Tool    Tool
	Mapping MCPToolMapping
}

// MCPToolMapping stores the information needed to convert an MCP call back to an OpenAPI call.
type MCPToolMapping struct {
	OpenAPIPath   string                  `json:"openapi_path"`
	HTTPMethod    string                  `json:"http_method"`
	ParamMappings map[string]ParamMapping `json:"param_mappings"`
	BodyMapping   BodyMapping             `json:"body_mapping"`
}

// ParamMapping defines the source of an MCP property that came from an OpenAPI parameter.
type ParamMapping struct {
	Source     string `json:"source"`      // "path", "query", "header"
	SourceName string `json:"source_name"` // The original parameter name
}

// BodyMapping defines how to reconstruct the request body.
type BodyMapping struct {
	// "wrapped" means the original was a primitive/array and is now in a single property.
	// "merged" means the original was an object and its properties were merged.
	Strategy         string   `json:"strategy"`
	WrappedProperty  string   `json:"wrapped_property,omitempty"` // Name of the property holding the wrapped body
	SourceProperties []string `json:"source_properties"`          // List of MCP properties that belong in the body
}
