package models

// OpenAPISpec represents the root of an OpenAPI 3 specification.
// We only define the fields we need for the conversion.
type OpenAPISpec struct {
	Paths      map[string]PathItem    `json:"paths"`
	Components map[string]interface{} `json:"components"`
}

// PathItem represents the operations available on a single path.
type PathItem map[string]Operation

// Operation represents a single API operation on a path.
type Operation struct {
	Summary     string      `json:"summary"`
	Description string      `json:"description"`
	OperationID string      `json:"operationId"`
	Parameters  []Parameter `json:"parameters"`
	RequestBody RequestBody `json:"requestBody"`
}

// Parameter defines a single operation parameter.
type Parameter struct {
	Name        string    `json:"name"`
	In          string    `json:"in"` // "query", "header", "path", "cookie"
	Description string    `json:"description"`
	Required    bool      `json:"required"`
	Schema      SchemaDef `json:"schema"`
}

// RequestBody represents the body of the request.
type RequestBody struct {
	Description string               `json:"description"`
	Content     map[string]MediaType `json:"content"`
	Required    bool                 `json:"required"`
}

// MediaType represents a single media type for the request body.
type MediaType struct {
	Schema SchemaDef `json:"schema"`
}

// SchemaDef is a flexible representation of a JSON Schema.
// Using map[string]interface{} allows us to handle any valid JSON Schema structure.
type SchemaDef map[string]interface{}
