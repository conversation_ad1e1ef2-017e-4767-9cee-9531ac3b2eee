// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: mcp_tool.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createMCPTool = `-- name: CreateMCPTool :one
INSERT INTO mcp_tools (
    tool_name,
    description,
    input_schema
) VALUES (
             $1, $2, $3
         )
    RETURNING id, tool_name, description, input_schema, created_at, updated_at
`

type CreateMCPToolParams struct {
	ToolName    string      `json:"tool_name"`
	Description pgtype.Text `json:"description"`
	InputSchema []byte      `json:"input_schema"`
}

// sql/query/mcp_tool.sql
// Inserts a new MCP tool into the database and returns it.
func (q *Queries) CreateMCPTool(ctx context.Context, arg CreateMCPToolParams) (McpTool, error) {
	row := q.db.QueryRow(ctx, createMCPTool, arg.ToolName, arg.Description, arg.InputSchema)
	var i McpTool
	err := row.Scan(
		&i.ID,
		&i.ToolName,
		&i.Description,
		&i.InputSchema,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createMCPToolMapping = `-- name: CreateMCPToolMapping :one
INSERT INTO mcp_tool_mappings (
    mcp_tool_id,
    openapi_path,
    http_method,
    param_mappings,
    body_mapping
) VALUES (
             $1, $2, $3, $4, $5
         )
    RETURNING id, mcp_tool_id, openapi_path, http_method, param_mappings, body_mapping, created_at
`

type CreateMCPToolMappingParams struct {
	McpToolID     int64  `json:"mcp_tool_id"`
	OpenapiPath   string `json:"openapi_path"`
	HttpMethod    string `json:"http_method"`
	ParamMappings []byte `json:"param_mappings"`
	BodyMapping   []byte `json:"body_mapping"`
}

// Inserts a new mapping record for an MCP tool.
func (q *Queries) CreateMCPToolMapping(ctx context.Context, arg CreateMCPToolMappingParams) (McpToolMapping, error) {
	row := q.db.QueryRow(ctx, createMCPToolMapping,
		arg.McpToolID,
		arg.OpenapiPath,
		arg.HttpMethod,
		arg.ParamMappings,
		arg.BodyMapping,
	)
	var i McpToolMapping
	err := row.Scan(
		&i.ID,
		&i.McpToolID,
		&i.OpenapiPath,
		&i.HttpMethod,
		&i.ParamMappings,
		&i.BodyMapping,
		&i.CreatedAt,
	)
	return i, err
}

const createProfile = `-- name: CreateProfile :one
INSERT INTO profiles (name, description, path_segment) VALUES ($1, $2, $3) RETURNING id, name, description, path_segment, created_at, updated_at
`

type CreateProfileParams struct {
	Name        string      `json:"name"`
	Description pgtype.Text `json:"description"`
	PathSegment string      `json:"path_segment"`
}

// Creates a new profile.
func (q *Queries) CreateProfile(ctx context.Context, arg CreateProfileParams) (Profile, error) {
	row := q.db.QueryRow(ctx, createProfile, arg.Name, arg.Description, arg.PathSegment)
	var i Profile
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.PathSegment,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createProfileTool = `-- name: CreateProfileTool :one
INSERT INTO profile_tools (
    profile_id,
    tool_id,
    acl
) VALUES (
    $1, $2, $3
)
RETURNING profile_id, tool_id, acl
`

type CreateProfileToolParams struct {
	ProfileID int64  `json:"profile_id"`
	ToolID    int64  `json:"tool_id"`
	Acl       string `json:"acl"`
}

// Links a profile to an MCP tool with a specific ACL.
func (q *Queries) CreateProfileTool(ctx context.Context, arg CreateProfileToolParams) (ProfileTool, error) {
	row := q.db.QueryRow(ctx, createProfileTool, arg.ProfileID, arg.ToolID, arg.Acl)
	var i ProfileTool
	err := row.Scan(&i.ProfileID, &i.ToolID, &i.Acl)
	return i, err
}

const createRole = `-- name: CreateRole :one
INSERT INTO roles (name) VALUES ($1) RETURNING id, name, created_at, updated_at
`

// Creates a new role.
func (q *Queries) CreateRole(ctx context.Context, name string) (Role, error) {
	row := q.db.QueryRow(ctx, createRole, name)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createUser = `-- name: CreateUser :one
INSERT INTO users (username, password_hash) VALUES ($1, $2) RETURNING id, username, password_hash, created_at, updated_at
`

type CreateUserParams struct {
	Username     string `json:"username"`
	PasswordHash string `json:"password_hash"`
}

// Creates a new user.
func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, createUser, arg.Username, arg.PasswordHash)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.PasswordHash,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createUserRole = `-- name: CreateUserRole :one
INSERT INTO user_roles (user_id, role_id) VALUES ($1, $2) RETURNING user_id, role_id
`

type CreateUserRoleParams struct {
	UserID int64 `json:"user_id"`
	RoleID int64 `json:"role_id"`
}

// Assigns a role to a user.
func (q *Queries) CreateUserRole(ctx context.Context, arg CreateUserRoleParams) (UserRole, error) {
	row := q.db.QueryRow(ctx, createUserRole, arg.UserID, arg.RoleID)
	var i UserRole
	err := row.Scan(&i.UserID, &i.RoleID)
	return i, err
}

const deleteProfile = `-- name: DeleteProfile :exec
DELETE FROM profiles WHERE id = $1
`

// Deletes a profile by its ID.
func (q *Queries) DeleteProfile(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteProfile, id)
	return err
}

const deleteProfileTool = `-- name: DeleteProfileTool :exec
DELETE FROM profile_tools WHERE profile_id = $1 AND tool_id = $2
`

type DeleteProfileToolParams struct {
	ProfileID int64 `json:"profile_id"`
	ToolID    int64 `json:"tool_id"`
}

// Deletes a profile-tool association.
func (q *Queries) DeleteProfileTool(ctx context.Context, arg DeleteProfileToolParams) error {
	_, err := q.db.Exec(ctx, deleteProfileTool, arg.ProfileID, arg.ToolID)
	return err
}

const deleteUserRole = `-- name: DeleteUserRole :exec
DELETE FROM user_roles WHERE user_id = $1 AND role_id = $2
`

type DeleteUserRoleParams struct {
	UserID int64 `json:"user_id"`
	RoleID int64 `json:"role_id"`
}

// Removes a role from a user.
func (q *Queries) DeleteUserRole(ctx context.Context, arg DeleteUserRoleParams) error {
	_, err := q.db.Exec(ctx, deleteUserRole, arg.UserID, arg.RoleID)
	return err
}

const getMCPToolByID = `-- name: GetMCPToolByID :one
SELECT id, tool_name, description, input_schema, created_at, updated_at FROM mcp_tools
WHERE id = $1 LIMIT 1
`

// Retrieves a single MCP tool by its unique ID.
func (q *Queries) GetMCPToolByID(ctx context.Context, id int64) (McpTool, error) {
	row := q.db.QueryRow(ctx, getMCPToolByID, id)
	var i McpTool
	err := row.Scan(
		&i.ID,
		&i.ToolName,
		&i.Description,
		&i.InputSchema,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getMCPToolByName = `-- name: GetMCPToolByName :one
SELECT id, tool_name, description, input_schema, created_at, updated_at FROM mcp_tools
WHERE tool_name = $1 LIMIT 1
`

// Retrieves a single MCP tool by its unique name.
func (q *Queries) GetMCPToolByName(ctx context.Context, toolName string) (McpTool, error) {
	row := q.db.QueryRow(ctx, getMCPToolByName, toolName)
	var i McpTool
	err := row.Scan(
		&i.ID,
		&i.ToolName,
		&i.Description,
		&i.InputSchema,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getProfileByID = `-- name: GetProfileByID :one
SELECT id, name, description, path_segment, created_at, updated_at FROM profiles WHERE id = $1 LIMIT 1
`

// Retrieves a profile by its ID.
func (q *Queries) GetProfileByID(ctx context.Context, id int64) (Profile, error) {
	row := q.db.QueryRow(ctx, getProfileByID, id)
	var i Profile
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.PathSegment,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getProfileByName = `-- name: GetProfileByName :one
SELECT id, name, description, path_segment, created_at, updated_at FROM profiles WHERE name = $1 LIMIT 1
`

// Retrieves a profile by its name.
func (q *Queries) GetProfileByName(ctx context.Context, name string) (Profile, error) {
	row := q.db.QueryRow(ctx, getProfileByName, name)
	var i Profile
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.PathSegment,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getProfileByPathSegment = `-- name: GetProfileByPathSegment :one
SELECT id, name, description, path_segment, created_at, updated_at FROM profiles WHERE path_segment = $1 LIMIT 1
`

// Retrieves a profile by its path segment.
func (q *Queries) GetProfileByPathSegment(ctx context.Context, pathSegment string) (Profile, error) {
	row := q.db.QueryRow(ctx, getProfileByPathSegment, pathSegment)
	var i Profile
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.PathSegment,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getRoleByName = `-- name: GetRoleByName :one
SELECT id, name, created_at, updated_at FROM roles WHERE name = $1 LIMIT 1
`

// Retrieves a role by its name.
func (q *Queries) GetRoleByName(ctx context.Context, name string) (Role, error) {
	row := q.db.QueryRow(ctx, getRoleByName, name)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getToolWithMapping = `-- name: GetToolWithMapping :one
SELECT
    t.id as tool_id,
    t.tool_name,
    t.description,
    t.input_schema,
    m.id as mapping_id,
    m.openapi_path,
    m.http_method,
    m.param_mappings,
    m.body_mapping
FROM
    mcp_tools t
        JOIN
    mcp_tool_mappings m ON t.id = m.mcp_tool_id
WHERE
    t.tool_name = $1
`

type GetToolWithMappingRow struct {
	ToolID        int64       `json:"tool_id"`
	ToolName      string      `json:"tool_name"`
	Description   pgtype.Text `json:"description"`
	InputSchema   []byte      `json:"input_schema"`
	MappingID     int64       `json:"mapping_id"`
	OpenapiPath   string      `json:"openapi_path"`
	HttpMethod    string      `json:"http_method"`
	ParamMappings []byte      `json:"param_mappings"`
	BodyMapping   []byte      `json:"body_mapping"`
}

// Retrieves an MCP tool along with its corresponding mapping information.
func (q *Queries) GetToolWithMapping(ctx context.Context, toolName string) (GetToolWithMappingRow, error) {
	row := q.db.QueryRow(ctx, getToolWithMapping, toolName)
	var i GetToolWithMappingRow
	err := row.Scan(
		&i.ToolID,
		&i.ToolName,
		&i.Description,
		&i.InputSchema,
		&i.MappingID,
		&i.OpenapiPath,
		&i.HttpMethod,
		&i.ParamMappings,
		&i.BodyMapping,
	)
	return i, err
}

const getUserByUsername = `-- name: GetUserByUsername :one
SELECT id, username, password_hash, created_at, updated_at FROM users WHERE username = $1 LIMIT 1
`

// Retrieves a user by their username.
func (q *Queries) GetUserByUsername(ctx context.Context, username string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByUsername, username)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Username,
		&i.PasswordHash,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const listMCPTools = `-- name: ListMCPTools :many
SELECT id, tool_name, description, input_schema, created_at, updated_at FROM mcp_tools
ORDER BY tool_name
`

// Lists all MCP tools stored in the database.
func (q *Queries) ListMCPTools(ctx context.Context) ([]McpTool, error) {
	rows, err := q.db.Query(ctx, listMCPTools)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []McpTool
	for rows.Next() {
		var i McpTool
		if err := rows.Scan(
			&i.ID,
			&i.ToolName,
			&i.Description,
			&i.InputSchema,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listMCPToolsByProfile = `-- name: ListMCPToolsByProfile :many
SELECT
    t.id, t.tool_name, t.description, t.input_schema, t.created_at, t.updated_at 
FROM
    mcp_tools t
JOIN
    profile_tools pt ON t.id = pt.tool_id
JOIN
    profiles p ON pt.profile_id = p.id
WHERE
    p.name = $1
ORDER BY
    t.tool_name
`

// Lists MCP tools associated with a specific profile.
func (q *Queries) ListMCPToolsByProfile(ctx context.Context, name string) ([]McpTool, error) {
	rows, err := q.db.Query(ctx, listMCPToolsByProfile, name)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []McpTool
	for rows.Next() {
		var i McpTool
		if err := rows.Scan(
			&i.ID,
			&i.ToolName,
			&i.Description,
			&i.InputSchema,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listProfiles = `-- name: ListProfiles :many
SELECT id, name, description, path_segment, created_at, updated_at FROM profiles ORDER BY name
`

// Lists all profiles.
func (q *Queries) ListProfiles(ctx context.Context) ([]Profile, error) {
	rows, err := q.db.Query(ctx, listProfiles)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Profile
	for rows.Next() {
		var i Profile
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.PathSegment,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUsers = `-- name: ListUsers :many
SELECT id, username, password_hash, created_at, updated_at FROM users ORDER BY username
`

// Lists all users.
func (q *Queries) ListUsers(ctx context.Context) ([]User, error) {
	rows, err := q.db.Query(ctx, listUsers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Username,
			&i.PasswordHash,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateProfile = `-- name: UpdateProfile :one
UPDATE profiles
SET
    name = $2,
    description = $3,
    path_segment = $4,
    updated_at = NOW()
WHERE
    id = $1
RETURNING id, name, description, path_segment, created_at, updated_at
`

type UpdateProfileParams struct {
	ID          int64       `json:"id"`
	Name        string      `json:"name"`
	Description pgtype.Text `json:"description"`
	PathSegment string      `json:"path_segment"`
}

// Updates an existing profile.
func (q *Queries) UpdateProfile(ctx context.Context, arg UpdateProfileParams) (Profile, error) {
	row := q.db.QueryRow(ctx, updateProfile,
		arg.ID,
		arg.Name,
		arg.Description,
		arg.PathSegment,
	)
	var i Profile
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.PathSegment,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
