package db

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
)

// ConnectDB establishes a connection to the PostgreSQL database.
var ConnectDB = func(connectionString string) (*pgx.Conn, error) {
	conn, err := pgx.Connect(context.Background(), connectionString)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to database: %w", err)
	}

	err = conn.Ping(context.Background())
	if err != nil {
		conn.Close(context.Background())
		return nil, fmt.<PERSON><PERSON>rf("database ping failed: %w", err)
	}

	return conn, nil
}
