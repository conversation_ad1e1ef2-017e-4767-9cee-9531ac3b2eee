// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type McpTool struct {
	ID int64 `json:"id"`
	// The unique name for the MCP tool, e.g., createUser.
	ToolName string `json:"tool_name"`
	// The description of what the tool does, taken from the OpenAPI summary.
	Description pgtype.Text `json:"description"`
	// The JSON schema for the tool's input, combining all parameters.
	InputSchema []byte             `json:"input_schema"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `json:"updated_at"`
}

type McpToolMapping struct {
	ID int64 `json:"id"`
	// Foreign key to the mcp_tools table.
	McpToolID int64 `json:"mcp_tool_id"`
	// The original OpenAPI path template, e.g., /users/{userId}.
	OpenapiPath string `json:"openapi_path"`
	// The HTTP method for the endpoint (e.g., POST, GET).
	HttpMethod string `json:"http_method"`
	// A JSON object mapping MCP properties back to their OpenAPI parameter sources (path, query, header).
	ParamMappings []byte `json:"param_mappings"`
	// A JSON object describing how to reconstruct the original request body.
	BodyMapping []byte             `json:"body_mapping"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
}

type Profile struct {
	ID int64 `json:"id"`
	// The unique name for the profile, e.g., "billing_admin_tools".
	Name string `json:"name"`
	// A description of what this profile contains.
	Description pgtype.Text `json:"description"`
	// A unique string for the URL path, e.g., "billing".
	PathSegment string             `json:"path_segment"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `json:"updated_at"`
}

type ProfileTool struct {
	ProfileID int64 `json:"profile_id"`
	ToolID    int64 `json:"tool_id"`
	// Access control level for the tool in this profile.
	Acl string `json:"acl"`
}

type Role struct {
	ID int64 `json:"id"`
	// The unique name for the role, e.g., "admin", "viewer".
	Name      string             `json:"name"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
}

// Maps which roles have access to which profiles.
type RoleProfile struct {
	RoleID    int64 `json:"role_id"`
	ProfileID int64 `json:"profile_id"`
}

type User struct {
	ID int64 `json:"id"`
	// The unique username for the user.
	Username string `json:"username"`
	// Hashed password for the user.
	PasswordHash string             `json:"password_hash"`
	CreatedAt    pgtype.Timestamptz `json:"created_at"`
	UpdatedAt    pgtype.Timestamptz `json:"updated_at"`
}

// Maps users to roles.
type UserRole struct {
	UserID int64 `json:"user_id"`
	RoleID int64 `json:"role_id"`
}
