package tui

import (
	"github.com/charmbracelet/bubbles/textinput"
)

// newTextInput creates a new textinput.Model with common configurations.
func newTextInput(placeholder string, password bool) textinput.Model {
	ti := textinput.New()
	ti.Placeholder = placeholder
	ti.Prompt = "> "
	ti.CharLimit = 256
	ti.Width = 50
	if password {
		ti.EchoMode = textinput.EchoPassword
		ti.EchoCharacter = '•'
	}
	return ti
}
