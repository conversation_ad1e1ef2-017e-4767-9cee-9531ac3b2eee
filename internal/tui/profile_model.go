package tui

import (
	"context"
	"fmt"

	"github.com/charmbracelet/bubbles/textinput"
	"github.com/charmbracelet/bubbletea"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/ravan/suse-air/internal/db"
)

type profileModel struct {
	queries  db.Querier
	profiles []db.Profile
	cursor   int
	selected int // index of selected profile for details/update/delete
	state    profileState

	// Input fields for create/update
	nameInput        textinput.Model
	descriptionInput textinput.Model
	pathSegmentInput textinput.Model

	// Status message
	statusMessage string
}

type profileState int

const (
	profileList profileState = iota
	profileCreateForm
	profileDetail
	profileUpdateForm
	profileDeleteConfirm
)

func newProfileModel(queries *db.Queries) profileModel {
	pm := profileModel{
		queries: queries,
		state:   profileList,
	}
	pm.nameInput = newTextInput("Profile Name", false)
	pm.descriptionInput = newTextInput("Description", false)
	pm.pathSegmentInput = newTextInput("Path Segment", false)
	return pm
}

func (pm profileModel) Init() tea.Cmd {
	return pm.fetchProfilesCmd()
}

func (pm profileModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return pm, tea.Quit

		case "esc":
			if pm.state != profileList {
				pm.state = profileList
				pm.statusMessage = ""
				return pm, pm.fetchProfilesCmd()
			}

		case "up", "k":
			if pm.state == profileList && pm.cursor > 0 {
				pm.cursor--
			}

		case "down", "j":
			if pm.state == profileList && pm.cursor < len(pm.profiles)-1 {
				pm.cursor++
			}

		case "enter":
			if pm.state == profileList && len(pm.profiles) > 0 {
				pm.selected = pm.cursor
				pm.state = profileDetail
			}

		case "c": // Create a new profile
			if pm.state == profileList {
				pm.state = profileCreateForm
				pm.nameInput.Focus()
				pm.statusMessage = ""
				return pm, textinput.Blink
			}

		case "u": // Update selected profile
			if pm.state == profileDetail && len(pm.profiles) > 0 {
				pm.state = profileUpdateForm
				selectedProfile := pm.profiles[pm.selected]
				pm.nameInput.SetValue(selectedProfile.Name)
				pm.descriptionInput.SetValue(selectedProfile.Description.String)
				pm.pathSegmentInput.SetValue(selectedProfile.PathSegment)
				pm.nameInput.Focus()
				pm.statusMessage = ""
				return pm, textinput.Blink
			}

		case "d": // Delete the selected profile
			if pm.state == profileDetail && len(pm.profiles) > 0 {
				pm.state = profileDeleteConfirm
				pm.statusMessage = ""
			}

		case "y", "Y": // Confirm delete
			if pm.state == profileDeleteConfirm && len(pm.profiles) > 0 {
				profileToDelete := pm.profiles[pm.selected]
				return pm, pm.deleteProfileCmd(profileToDelete.ID)
			}

		case "n", "N": // Cancel delete
			if pm.state == profileDeleteConfirm {
				pm.state = profileDetail
				pm.statusMessage = "Deletion cancelled."
			}
		}

	case profilesFetchedMsg: // Profiles loaded
		pm.profiles = msg
		pm.cursor = 0
		pm.statusMessage = ""
		return pm, nil

	case profileCreatedMsg: // Profile created
		pm.statusMessage = fmt.Sprintf("Profile '%s' created successfully!", msg.Name)
		pm.state = profileList
		pm.nameInput.SetValue("")
		pm.descriptionInput.SetValue("")
		pm.pathSegmentInput.SetValue("")
		return pm, pm.fetchProfilesCmd()

	case profileUpdatedMsg: // Profile updated
		pm.statusMessage = fmt.Sprintf("Profile '%s' updated successfully!", msg.Name)
		pm.state = profileList
		return pm, pm.fetchProfilesCmd()

	case profileDeletedMsg: // Profile deleted
		pm.statusMessage = fmt.Sprintf("Profile '%s' deleted successfully!", msg)
		pm.state = profileList
		return pm, pm.fetchProfilesCmd()

	case errMsg: // Error occurred
		pm.statusMessage = fmt.Sprintf("Error: %s", msg.Error())
		return pm, nil
	}

	// Handle input for forms
	if pm.state == profileCreateForm || pm.state == profileUpdateForm {
		var cmd tea.Cmd
		pm.nameInput, cmd = pm.nameInput.Update(msg)
		cmds = append(cmds, cmd)
		pm.descriptionInput, cmd = pm.descriptionInput.Update(msg)
		cmds = append(cmds, cmd)
		pm.pathSegmentInput, cmd = pm.pathSegmentInput.Update(msg)
		cmds = append(cmds, cmd)

		switch msg := msg.(type) {
		case tea.KeyMsg:
			switch msg.String() {
			case "tab", "shift+tab", "enter":
				if msg.String() == "enter" && pm.nameInput.Focused() {
					// Do nothing, allow multiline input for description
				} else if msg.String() == "enter" && pm.descriptionInput.Focused() {
					// Do nothing, allow multiline input for description
				} else {
					// Move focus
					if pm.nameInput.Focused() {
						pm.nameInput.Blur()
						pm.descriptionInput.Focus()
						cmds = append(cmds, textinput.Blink)
					} else if pm.descriptionInput.Focused() {
						pm.descriptionInput.Blur()
						pm.pathSegmentInput.Focus()
						cmds = append(cmds, textinput.Blink)
					} else if pm.pathSegmentInput.Focused() {
						pm.pathSegmentInput.Blur()
						// Submit form
						if pm.state == profileCreateForm {
							cmds = append(cmds, pm.createProfileCmd(pm.nameInput.Value(), pm.descriptionInput.Value(), pm.pathSegmentInput.Value()))
						} else if pm.state == profileUpdateForm {
							cmds = append(cmds, pm.updateProfileCmd(pm.profiles[pm.selected].ID, pm.nameInput.Value(), pm.descriptionInput.Value(), pm.pathSegmentInput.Value()))
						}
					}
				}
			}
		}
	}

	return pm, tea.Batch(cmds...)
}

func (pm profileModel) View() string {
	s := ""

	s += "\nProfile Management\n"
	s += "------------------\n"

	switch pm.state {
	case profileList:
		s += "Profiles:\n"
		if len(pm.profiles) == 0 {
			s += "  No profiles found. Press 'c' to create one.\n"
		} else {
			for i, p := range pm.profiles {
				cursor := " "
				if pm.cursor == i {
					cursor = ">"
				}
				s += fmt.Sprintf("%s %s (Path: %s)\n", cursor, p.Name, p.PathSegment)
			}
			s += "\nPress 'enter' to view details, 'c' to create, 'q' to quit.\n"
		}

	case profileCreateForm:
		s += "Create New Profile:\n"
		s += fmt.Sprintf("  %s\n", pm.nameInput.View())
		s += fmt.Sprintf("  %s\n", pm.descriptionInput.View())
		s += fmt.Sprintf("  %s\n", pm.pathSegmentInput.View())
		s += "\nPress Tab to navigate, Enter to submit, Esc to cancel.\n"

	case profileDetail:
		if pm.selected >= 0 && pm.selected < len(pm.profiles) {
			profile := pm.profiles[pm.selected]
			s += fmt.Sprintf("Profile Details for '%s':\n", profile.Name)
			s += fmt.Sprintf("  ID: %d\n", profile.ID)
			s += fmt.Sprintf("  Name: %s\n", profile.Name)
			s += fmt.Sprintf("  Description: %s\n", profile.Description.String)
			s += fmt.Sprintf("  Path Segment: %s\n", profile.PathSegment)
			s += fmt.Sprintf("  Created At: %s\n", profile.CreatedAt.Time.Format("2006-01-02 15:04:05"))
			s += fmt.Sprintf("  Updated At: %s\n", profile.UpdatedAt.Time.Format("2006-01-02 15:04:05"))
			s += "\nPress 'u' to update, 'd' to delete, 'esc' to go back to list.\n"
		} else {
			s += "Profile not found.\n"
			pm.state = profileList // Fallback to list
		}

	case profileUpdateForm:
		selectedProfile := pm.profiles[pm.selected]
		s += fmt.Sprintf("Update Profile '%s':\n", selectedProfile.Name)
		s += fmt.Sprintf("  %s\n", pm.nameInput.View())
		s += fmt.Sprintf("  %s\n", pm.descriptionInput.View())
		s += fmt.Sprintf("  %s\n", pm.pathSegmentInput.View())
		s += "\nPress Tab to navigate, Enter to submit, Esc to cancel.\n"

	case profileDeleteConfirm:
		selectedProfile := pm.profiles[pm.selected]
		s += fmt.Sprintf("Are you sure you want to delete profile '%s'? (y/N)\n", selectedProfile.Name)
	}

	if pm.statusMessage != "" {
		s += fmt.Sprintf("\nStatus: %s\n", pm.statusMessage)
	}

	return s
}

// Commands

type profilesFetchedMsg []db.Profile
type profileCreatedMsg db.Profile
type profileUpdatedMsg db.Profile
type profileDeletedMsg string
type errMsg error

func (pm profileModel) fetchProfilesCmd() tea.Cmd {
	return func() tea.Msg {
		profiles, err := pm.queries.ListProfiles(context.Background())
		if err != nil {
			return errMsg(fmt.Errorf("could not fetch profiles: %w", err))
		}
		return profilesFetchedMsg(profiles)
	}
}

func (pm profileModel) createProfileCmd(name, description, pathSegment string) tea.Cmd {
	return func() tea.Msg {
		profile, err := pm.queries.CreateProfile(context.Background(), db.CreateProfileParams{
			Name:        name,
			Description: pgtype.Text{String: description, Valid: description != ""},
			PathSegment: pathSegment,
		})
		if err != nil {
			return errMsg(fmt.Errorf("could not create profile: %w", err))
		}
		return profileCreatedMsg(profile)
	}
}

func (pm profileModel) updateProfileCmd(id int64, name, description, pathSegment string) tea.Cmd {
	return func() tea.Msg {
		profile, err := pm.queries.UpdateProfile(context.Background(), db.UpdateProfileParams{
			ID:          id,
			Name:        name,
			Description: pgtype.Text{String: description, Valid: description != ""},
			PathSegment: pathSegment,
		})
		if err != nil {
			return errMsg(fmt.Errorf("could not update profile: %w", err))
		}
		return profileUpdatedMsg(profile)
	}
}

func (pm profileModel) deleteProfileCmd(id int64) tea.Cmd {
	return func() tea.Msg {
		// Get a profile name before deleting for a status message
		profile, err := pm.queries.GetProfileByID(context.Background(), id)
		if err != nil {
			return errMsg(fmt.Errorf("could not find profile to delete: %w", err))
		}
		profileName := profile.Name

		err = pm.queries.DeleteProfile(context.Background(), id)
		if err != nil {
			return errMsg(fmt.Errorf("could not delete profile: %w", err))
		}
		return profileDeletedMsg(profileName)
	}
}
