package tui

import (
	"context"
	"fmt"

	"github.com/charmbracelet/bubbles/textinput"
	"github.com/charmbracelet/bubbletea"
	"github.com/ravan/suse-air/internal/db"
)

type toolModel struct {
	queries         db.Querier
	profiles        []db.Profile
	tools           []db.ListMCPToolsByProfileRow
	profileCursor   int
	toolCursor      int
	selectedProfile db.Profile
	state           toolState

	// Input fields for associate
	aclInput textinput.Model

	statusMessage string
}

type toolState int

const (
	toolProfileList toolState = iota
	toolToolList
	toolAssociateForm
	toolDisassociateConfirm
)

func newToolModel(queries db.Querier) toolModel {
	tm := toolModel{
		queries: queries,
		state:   toolProfileList,
	}
	tm.aclInput = newTextInput("ACL (e.g., EXECUTE, READ_ONLY)", false)
	return tm
}

func (tm toolModel) Init() tea.Cmd {
	return tm.fetchProfilesCmd()
}

func (tm toolModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {

	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return tm, tea.Quit

		case "esc":
			if tm.state == toolToolList {
				tm.state = toolProfileList
				tm.statusMessage = ""
				return tm, tm.fetchProfilesCmd()
			} else if tm.state == toolAssociateForm || tm.state == toolDisassociateConfirm {
				tm.state = toolToolList
				tm.statusMessage = ""
				return tm, tm.fetchToolsByProfileCmd(tm.selectedProfile.Name)
			}

		case "up", "k":
			if tm.state == toolProfileList && tm.profileCursor > 0 {
				tm.profileCursor--
			} else if tm.state == toolToolList && tm.toolCursor > 0 {
				tm.toolCursor--
			}

		case "down", "j":
			if tm.state == toolProfileList && tm.profileCursor < len(tm.profiles)-1 {
				tm.profileCursor++
			} else if tm.state == toolToolList && tm.toolCursor < len(tm.tools)-1 {
				tm.toolCursor++
			}

		case "enter":
			if tm.state == toolProfileList && len(tm.profiles) > 0 {
				tm.selectedProfile = tm.profiles[tm.profileCursor]
				tm.state = toolToolList
				return tm, tm.fetchToolsByProfileCmd(tm.selectedProfile.Name)
			} else if tm.state == toolToolList && len(tm.tools) > 0 {
				tm.state = toolAssociateForm // Or some other action for selected tool
				tm.aclInput.Focus()
				return tm, textinput.Blink
			}

		case "a": // Associate tool
			if tm.state == toolToolList && len(tm.tools) > 0 {
				tm.state = toolAssociateForm
				tm.aclInput.Focus()
				return tm, textinput.Blink
			}

		case "d": // Disassociate tool
			if tm.state == toolToolList && len(tm.tools) > 0 {
				tm.state = toolDisassociateConfirm
			}

		case "y", "Y": // Confirm disassociate
			if tm.state == toolDisassociateConfirm && len(tm.tools) > 0 {
				toolToDisassociate := tm.tools[tm.toolCursor]
				return tm, tm.disassociateToolCmd(tm.selectedProfile.ID, toolToDisassociate.ID)
			}

		case "n", "N": // Cancel disassociate
			if tm.state == toolDisassociateConfirm {
				tm.state = toolToolList
				tm.statusMessage = "Disassociation cancelled."
			}
		}

	case profilesFetchedMsg: // Profiles loaded
		tm.profiles = msg
		tm.profileCursor = 0
		tm.statusMessage = ""
		return tm, nil

	case toolsFetchedMsg: // Tools loaded for a profile
		tm.tools = msg
		tm.toolCursor = 0
		tm.statusMessage = ""
		return tm, nil

	case toolAssociatedMsg: // Tool associated
		tm.statusMessage = fmt.Sprintf("Tool '%s' associated with profile '%s' successfully!", msg.ToolName, tm.selectedProfile.Name)
		tm.state = toolToolList
		return tm, tm.fetchToolsByProfileCmd(tm.selectedProfile.Name)

	case toolDisassociatedMsg: // Tool disassociated
		tm.statusMessage = fmt.Sprintf("Tool '%s' disassociated from profile '%s' successfully!", msg, tm.selectedProfile.Name)
		tm.state = toolToolList
		return tm, tm.fetchToolsByProfileCmd(tm.selectedProfile.Name)

	case errMsg: // Error occurred
		tm.statusMessage = fmt.Sprintf("Error: %s", msg.Error())
		return tm, nil
	}

	// Handle input for forms
	if tm.state == toolAssociateForm {
		switch msg := msg.(type) {
		case tea.KeyMsg:
			switch msg.String() {
			case "enter":
				selectedTool := tm.tools[tm.toolCursor]
				cmd := tm.associateToolCmd(tm.selectedProfile.ID, selectedTool.ID, tm.aclInput.Value())
				cmds = append(cmds, cmd)
			}
		}
		var aclCmd tea.Cmd
		tm.aclInput, aclCmd = tm.aclInput.Update(msg)
		cmds = append(cmds, aclCmd)
	}

	return tm, tea.Batch(cmds...)
}

func (tm toolModel) View() string {
	s := ""

	s += "\nTool Association Management\n"
	s += "---------------------------\n"

	switch tm.state {
	case toolProfileList:
		s += "Select a Profile:\n"
		if len(tm.profiles) == 0 {
			s += "  No profiles found.\n"
		} else {
			for i, p := range tm.profiles {
				cursor := " "
				if tm.profileCursor == i {
					cursor = ">"
				}
				s += fmt.Sprintf("%s %s\n", cursor, p.Name)
			}
			s += "\nPress 'enter' to select, 'q' to quit.\n"
		}

	case toolToolList:
		s += fmt.Sprintf("Tools for Profile '%s':\n", tm.selectedProfile.Name)
		if len(tm.tools) == 0 {
			s += "  No tools associated with this profile. Press 'a' to associate one.\n"
		} else {
			for i, t := range tm.tools {
				cursor := " "
				if tm.toolCursor == i {
					cursor = ">"
				}
				s += fmt.Sprintf("%s %s (ID: %d)\n", cursor, t.ToolName, t.ID)
			}
			s += "\nPress 'a' to associate, 'd' to disassociate, 'esc' to go back to profiles.\n"
		}

	case toolAssociateForm:
		selectedTool := tm.tools[tm.toolCursor]
		s += fmt.Sprintf("Associate Tool '%s' with Profile '%s':\n", selectedTool.ToolName, tm.selectedProfile.Name)
		s += fmt.Sprintf("  %s\n", tm.aclInput.View())
		s += "\nPress Enter to submit, Esc to cancel.\n"

	case toolDisassociateConfirm:
		selectedTool := tm.tools[tm.toolCursor]
		s += fmt.Sprintf("Are you sure you want to disassociate tool '%s' from profile '%s'? (y/N)\n", selectedTool.ToolName, tm.selectedProfile.Name)
	}

	if tm.statusMessage != "" {
		s += fmt.Sprintf("\nStatus: %s\n", tm.statusMessage)
	}

	return s
}

// Commands

type toolsFetchedMsg []db.ListMCPToolsByProfileRow
type toolAssociatedMsg struct {
	ProfileTool db.ProfileTool
	ToolName    string
}
type toolDisassociatedMsg string

func (tm toolModel) fetchProfilesCmd() tea.Cmd {
	return func() tea.Msg {
		profiles, err := tm.queries.ListProfiles(context.Background())
		if err != nil {
			return errMsg(fmt.Errorf("could not fetch profiles: %w", err))
		}
		return profilesFetchedMsg(profiles)
	}
}

func (tm toolModel) fetchToolsByProfileCmd(profileName string) tea.Cmd {
	return func() tea.Msg {
		tools, err := tm.queries.ListMCPToolsByProfile(context.Background(), profileName)
		if err != nil {
			return errMsg(fmt.Errorf("could not fetch tools for profile %s: %w", profileName, err))
		}
		return toolsFetchedMsg(tools)
	}
}

func (tm toolModel) associateToolCmd(profileID, toolID int64, acl string) tea.Cmd {
	return func() tea.Msg {
		profileTool, err := tm.queries.CreateProfileTool(context.Background(), db.CreateProfileToolParams{
			ProfileID: profileID,
			ToolID:    toolID,
			Acl:       acl,
		})
		if err != nil {
			return errMsg(fmt.Errorf("could not associate tool: %w", err))
		}
		tool, err := tm.queries.GetMCPToolByID(context.Background(), toolID)
		if err != nil {
			return errMsg(fmt.Errorf("could not retrieve associated tool: %w", err))
		}
		return toolAssociatedMsg{ProfileTool: profileTool, ToolName: tool.ToolName}
	}
}

func (tm toolModel) disassociateToolCmd(profileID, toolID int64) tea.Cmd {
	return func() tea.Msg {
		// Get tool name before deleting for status message
		tool, err := tm.queries.GetMCPToolByID(context.Background(), toolID)
		if err != nil {
			return errMsg(fmt.Errorf("could not find tool to disassociate: %w", err))
		}
		toolName := tool.ToolName

		err = tm.queries.DeleteProfileTool(context.Background(), db.DeleteProfileToolParams{
			ProfileID: profileID,
			ToolID:    toolID,
		})
		if err != nil {
			return errMsg(fmt.Errorf("could not disassociate tool: %w", err))
		}
		return toolDisassociatedMsg(toolName)
	}
}
