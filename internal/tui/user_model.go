package tui

import (
	"context"
	"fmt"

	"github.com/charmbracelet/bubbles/textinput"
	"github.com/charmbracelet/bubbletea"
	"github.com/ravan/suse-air/internal/db"
	"golang.org/x/crypto/bcrypt"
)

type userModel struct {
	queries      *db.Queries
	users        []db.User
	roles        []db.Role
	cursor       int
	selectedUser db.User
	state        userState

	// Input fields for create/assign-role
	usernameInput textinput.Model
	passwordInput textinput.Model
	roleInput     textinput.Model

	statusMessage string
}

type userState int

const (
	userList userState = iota
	userCreateForm
	userAssignRoleForm
	userRemoveRoleConfirm
)

func newUserModel(queries *db.Queries) userModel {
	um := userModel{
		queries: queries,
		state:   userList,
	}
	um.usernameInput = newTextInput("Username", false)
	um.passwordInput = newTextInput("Password", true)
	um.roleInput = newTextInput("Role Name", false)
	return um
}

func (um userModel) Init() tea.Cmd {
	return um.fetchUsersCmd()
}

func (um userModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return um, tea.Quit

		case "esc":
			if um.state != userList {
				um.state = userList
				um.statusMessage = ""
				return um, um.fetchUsersCmd()
			}

		case "up", "k":
			if um.state == userList && um.cursor > 0 {
				um.cursor--
			}

		case "down", "j":
			if um.state == userList && um.cursor < len(um.users)-1 {
				um.cursor++
			}

		case "c": // Create new user
			if um.state == userList {
				um.state = userCreateForm
				um.usernameInput.Focus()
				um.statusMessage = ""
				return um, um.usernameInput.Blink
			}

		case "a": // Assign role to user
			if um.state == userList && len(um.users) > 0 {
				um.selectedUser = um.users[um.cursor]
				um.state = userAssignRoleForm
				um.roleInput.Focus()
				um.statusMessage = ""
				return um, um.roleInput.Blink
			}

		case "r": // Remove role from user
			if um.state == userList && len(um.users) > 0 {
				um.selectedUser = um.users[um.cursor]
				um.state = userRemoveRoleConfirm
				um.statusMessage = ""
			}

		case "y", "Y": // Confirm remove role
			if um.state == userRemoveRoleConfirm && len(um.users) > 0 {
				return um, um.removeRoleFromUserCmd(um.selectedUser.ID, um.roleInput.Value())
			}

		case "n", "N": // Cancel remove role
			if um.state == userRemoveRoleConfirm {
				um.state = userList
				um.statusMessage = "Role removal cancelled."
			}
		}

	case usersFetchedMsg: // Users loaded
		um.users = msg
		um.cursor = 0
		um.statusMessage = ""
		return um, nil

	case userCreatedMsg: // User created
		um.statusMessage = fmt.Sprintf("User '%s' created successfully!", msg.Username)
		um.state = userList
		um.usernameInput.SetValue("")
		um.passwordInput.SetValue("")
		return um, um.fetchUsersCmd()

	case roleAssignedMsg: // Role assigned
		um.statusMessage = fmt.Sprintf("Role assigned to user '%s' successfully!", um.selectedUser.Username)
		um.state = userList
		um.roleInput.SetValue("")
		return um, um.fetchUsersCmd()

	case roleRemovedMsg: // Role removed
		um.statusMessage = fmt.Sprintf("Role removed from user '%s' successfully!", um.selectedUser.Username)
		um.state = userList
		um.roleInput.SetValue("")
		return um, um.fetchUsersCmd()

	case errMsg: // Error occurred
		um.statusMessage = fmt.Sprintf("Error: %s", msg.Error())
		return um, nil
	}

	// Handle input for forms
	var usernameCmd, passwordCmd, roleCmd, cmd tea.Cmd
	if um.state == userCreateForm {
		um.usernameInput, usernameCmd = um.usernameInput.Update(msg)
		um.passwordInput, passwordCmd = um.passwordInput.Update(msg)
		cmds = append(cmds, usernameCmd, passwordCmd)

		switch msg := msg.(type) {
		case tea.KeyMsg:
			switch msg.String() {
			case "tab", "shift+tab", "enter":
				if msg.String() == "enter" && um.passwordInput.Focused() {
					// Submit form
					cmd = um.createUserCmd(um.usernameInput.Value(), um.passwordInput.Value())
				} else {
					// Move focus
					if um.usernameInput.Focused() {
						um.usernameInput.Blur()
						um.passwordInput.Focus()
						cmd = um.passwordInput.Blink
					}
				}
			}
		}
	} else if um.state == userAssignRoleForm {
		um.roleInput, roleCmd = um.roleInput.Update(msg)
		cmds = append(cmds, roleCmd)

		switch msg := msg.(type) {
		case tea.KeyMsg:
			switch msg.String() {
			case "enter":
				cmd = um.assignRoleToUserCmd(um.selectedUser.ID, um.roleInput.Value())
			}
		}
	}

	return um, tea.Batch(cmds...)
}

func (um userModel) View() string {
	s := ""

	s += "\nUser Management\n"
	s += "------------------\n"

	switch um.state {
	case userList:
		s += "Users:\n"
		if len(um.users) == 0 {
			s += "  No users found. Press 'c' to create one.\n"
		} else {
			for i, u := range um.users {
				cursor := " "
				if um.cursor == i {
					cursor = ">"
				}
				s += fmt.Sprintf("%s %s\n", cursor, u.Username)
			}
			s += "\nPress 'c' to create, 'a' to assign role, 'r' to remove role, 'q' to quit.\n"
		}

	case userCreateForm:
		s += "Create New User:\n"
		s += fmt.Sprintf("  %s\n", um.usernameInput.View())
		s += fmt.Sprintf("  %s\n", um.passwordInput.View())
		s += "\nPress Tab to navigate, Enter to submit, Esc to cancel.\n"

	case userAssignRoleForm:
		s += fmt.Sprintf("Assign Role to User '%s':\n", um.selectedUser.Username)
		s += fmt.Sprintf("  %s\n", um.roleInput.View())
		s += "\nPress Enter to submit, Esc to cancel.\n"

	case userRemoveRoleConfirm:
		s += fmt.Sprintf("Are you sure you want to remove role from user '%s'? (y/N)\n", um.selectedUser.Username)
	}

	if um.statusMessage != "" {
		s += fmt.Sprintf("\nStatus: %s\n", um.statusMessage)
	}

	return s
}

// Commands

type usersFetchedMsg []db.User
type userCreatedMsg db.User
type roleAssignedMsg db.UserRole
type roleRemovedMsg string

func (um userModel) fetchUsersCmd() tea.Cmd {
	return func() tea.Msg {
		users, err := um.queries.ListUsers(context.Background())
		if err != nil {
			return errMsg(fmt.Errorf("could not fetch users: %w", err))
		}
		return usersFetchedMsg(users)
	}
}

func (um userModel) createUserCmd(username, password string) tea.Cmd {
	return func() tea.Msg {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if err != nil {
			return errMsg(fmt.Errorf("could not hash password: %w", err))
		}

		user, err := um.queries.CreateUser(context.Background(), db.CreateUserParams{
			Username:     username,
			PasswordHash: string(hashedPassword),
		})
		if err != nil {
			return errMsg(fmt.Errorf("could not create user: %w", err))
		}
		return userCreatedMsg(user)
	}
}

func (um userModel) assignRoleToUserCmd(userID int64, roleName string) tea.Cmd {
	return func() tea.Msg {
		role, err := um.queries.GetRoleByName(context.Background(), roleName)
		if err != nil {
			return errMsg(fmt.Errorf("could not find role %s: %w", roleName, err))
		}

		userRole, err := um.queries.CreateUserRole(context.Background(), db.CreateUserRoleParams{
			UserID: userID,
			RoleID: role.ID,
		})
		if err != nil {
			return errMsg(fmt.Errorf("could not assign role: %w", err))
		}
		return roleAssignedMsg(userRole)
	}
}

func (um userModel) removeRoleFromUserCmd(userID int64, roleName string) tea.Cmd {
	return func() tea.Msg {
		role, err := um.queries.GetRoleByName(context.Background(), roleName)
		if err != nil {
			return errMsg(fmt.Errorf("could not find role %s: %w", roleName, err))
		}

		err = um.queries.DeleteUserRole(context.Background(), db.DeleteUserRoleParams{
			UserID: userID,
			RoleID: role.ID,
		})
		if err != nil {
			return errMsg(fmt.Errorf("could not remove role: %w", err))
		}
		return roleRemovedMsg(roleName)
	}
}
