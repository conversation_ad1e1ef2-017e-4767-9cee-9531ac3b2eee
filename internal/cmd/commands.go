package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/models"
	"github.com/ravan/suse-air/internal/schema"
	"github.com/ravan/suse-air/internal/server"
	"github.com/ravan/suse-air/internal/tui"

	"github.com/charmbracelet/bubbletea"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/urfave/cli/v2"
	"golang.org/x/crypto/bcrypt"
)

func RunConversion(dbConnectionString, filePath string) error {

	log.Println("--- Running in CONVERT mode ---")
	// Read and Parse OpenAPI File
	fileBytes, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read file %s: %w", filePath, err)
	}
	var openAPISpec models.OpenAPISpec
	if err := json.Unmarshal(fileBytes, &openAPISpec); err != nil {
		return fmt.Errorf("failed to unmarshal OpenAPI JSON: %w", err)
	}

	// Convert to MCP Tools and Mappings
	conversionResults, err := schema.ProcessOpenAPISpec(&openAPISpec)
	if err != nil {
		return fmt.Errorf("failed to convert OpenAPI spec: %w", err)
	}

	// Store Results in Database
	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)
	var createdCount int
	for _, result := range conversionResults {
		schemaBytes, _ := json.Marshal(result.Tool.InputSchema)
		paramMappingsBytes, _ := json.Marshal(result.Mapping.ParamMappings)
		bodyMappingBytes, _ := json.Marshal(result.Mapping.BodyMapping)

		toolParams := db.CreateMCPToolParams{
			ToolName:    result.Tool.Name,
			Description: pgtype.Text{String: result.Tool.Description, Valid: result.Tool.Description != ""},
			InputSchema: schemaBytes,
		}
		savedTool, err := queries.CreateMCPTool(ctx, toolParams)
		if err != nil {
			log.Printf("ERROR: Could not save tool %s: %v", result.Tool.Name, err)
			continue
		}
		log.Printf("DEBUG: Saved tool with ID: %d, Name: %s", savedTool.ID, savedTool.ToolName)

		mappingParams := db.CreateMCPToolMappingParams{
			McpToolID:     savedTool.ID,
			OpenapiPath:   result.Mapping.OpenAPIPath,
			HttpMethod:    result.Mapping.HTTPMethod,
			ParamMappings: paramMappingsBytes,
			BodyMapping:   bodyMappingBytes,
		}
		_, err = queries.CreateMCPToolMapping(ctx, mappingParams)
		if err != nil {
			log.Printf("ERROR: Could not save mapping for tool %s (tool ID %d): %v", result.Tool.Name, savedTool.ID, err)
			continue
		}
		log.Printf("DEBUG: Saved mapping for tool: %s (tool ID %d)", result.Tool.Name, savedTool.ID)
		createdCount++
	}
	log.Printf("Finished. Saved %d new tools with mappings.\n", createdCount)
	return nil
}

func RunExecution(dbConnectionString, toolName, args, baseURL string) error {
	log.Println("--- Running in EXECUTE mode ---")

	// Validate baseURL
	if baseURL == "" || !strings.HasPrefix(baseURL, "http") {
		return fmt.Errorf("the -base-url flag is required and must be a valid URL")
	}

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// 1. Fetch the tool and its mapping from the DB
	log.Printf("Fetching mapping for tool: %s\n", toolName)
	toolWithMapping, err := queries.GetToolWithMapping(ctx, toolName)
	if err != nil {
		return fmt.Errorf("error fetching tool '%s' from database: %w", toolName, err)
	}

	// 2. Unmarshal the provided arguments
	var mcpArgs map[string]interface{}
	if err := json.Unmarshal([]byte(args), &mcpArgs); err != nil {
		return fmt.Errorf("invalid JSON provided for -args flag: %w", err)
	}

	// 3. Use the executor to build the HTTP request
	log.Println("Building HTTP request from MCP arguments...")
	req, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	if err != nil {
		return fmt.Errorf("failed to build HTTP request: %w", err)
	}

	// 4. Execute the request
	log.Printf("Executing request: %s %s\n", req.Method, req.URL.String())
	client := schema.NewAPIClient()
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to execute API request: %w", err)
	}
	defer resp.Body.Close()

	// 5. Print the result
	log.Printf("API Response Status: %s\n", resp.Status)
	fmt.Println("--- API Response Body ---")
	if _, err := os.Stdout.ReadFrom(resp.Body); err != nil {
		log.Printf("Warning: could not read response body: %v", err)
	}
	fmt.Println("\n--- End Response ---")
	return nil
}

func RunServer(port int, dbConnectionString string) {
	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		log.Fatal("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)
	log.Printf("--- Starting MCP Server on port %d ---", port)
	mcpServer := server.NewServer(queries)
	//authHandler := server.AuthMiddleware(mcpServer)
	profileRouter := server.ProfileRouter(mcpServer)
	http.Handle("/mcp/", profileRouter)

	log.Fatal(http.ListenAndServe(fmt.Sprintf(":%d", port), nil))
}

func RunProfileCreate(c *cli.Context) error {
	name := c.String("name")
	description := c.String("description")
	pathSegment := c.String("path-segment")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	params := db.CreateProfileParams{
		Name:        name,
		Description: pgtype.Text{String: description, Valid: description != ""},
		PathSegment: pathSegment,
	}

	profile, err := queries.CreateProfile(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to create profile: %w", err)
	}

	fmt.Printf("Profile created successfully: ID=%d, Name=%s, PathSegment=%s\n", profile.ID, profile.Name, profile.PathSegment)
	return nil
}

func RunProfileList(c *cli.Context) error {
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	profiles, err := queries.ListProfiles(ctx)
	if err != nil {
		return fmt.Errorf("failed to list profiles: %w", err)
	}

	if len(profiles) == 0 {
		fmt.Println("No profiles found.")
		return nil
	}

	fmt.Println("Profiles:")
	for _, p := range profiles {
		fmt.Printf("  ID: %d, Name: %s, Description: %s, PathSegment: %s\n", p.ID, p.Name, p.Description.String, p.PathSegment)
	}
	return nil
}

func RunProfileGet(c *cli.Context) error {
	name := c.String("name")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	profile, err := queries.GetProfileByName(ctx, name)
	if err != nil {
		return fmt.Errorf("failed to get profile %s: %w", name, err)
	}

	fmt.Printf("Profile Details:\n")
	fmt.Printf("  ID: %d\n", profile.ID)
	fmt.Printf("  Name: %s\n", profile.Name)
	fmt.Printf("  Description: %s\n", profile.Description.String)
	fmt.Printf("  PathSegment: %s\n", profile.PathSegment)
	fmt.Printf("  CreatedAt: %s\n", profile.CreatedAt.Time.Format("2006-01-02 15:04:05"))
	fmt.Printf("  UpdatedAt: %s\n", profile.UpdatedAt.Time.Format("2006-01-02 15:04:05"))

	return nil
}

func RunProfileUpdate(c *cli.Context) error {
	name := c.String("name")
	newName := c.String("new-name")
	newDescription := c.String("new-description")
	newPathSegment := c.String("new-path-segment")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// First, get the existing profile to update its fields
	existingProfile, err := queries.GetProfileByName(ctx, name)
	if err != nil {
		return fmt.Errorf("failed to find profile %s for update: %w", name, err)
	}

	// Prepare update parameters, only updating if new values are provided
	updateParams := db.UpdateProfileParams{
		ID:          existingProfile.ID,
		Name:        existingProfile.Name,
		Description: existingProfile.Description,
		PathSegment: existingProfile.PathSegment,
	}

	if newName != "" {
		updateParams.Name = newName
	}
	if newDescription != "" {
		updateParams.Description = pgtype.Text{String: newDescription, Valid: true}
	}
	if newPathSegment != "" {
		updateParams.PathSegment = newPathSegment
	}

	profile, err := queries.UpdateProfile(ctx, updateParams)
	if err != nil {
		return fmt.Errorf("failed to update profile %s: %w", name, err)
	}

	fmt.Printf("Profile updated successfully: ID=%d, Name=%s, PathSegment=%s\n", profile.ID, profile.Name, profile.PathSegment)
	return nil
}

func RunProfileDelete(c *cli.Context) error {
	name := c.String("name")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// First, get the existing profile to get its ID
	existingProfile, err := queries.GetProfileByName(ctx, name)
	if err != nil {
		return fmt.Errorf("failed to find profile %s for deletion: %w", name, err)
	}

	err = queries.DeleteProfile(ctx, existingProfile.ID)
	if err != nil {
		return fmt.Errorf("failed to delete profile %s: %w", name, err)
	}

	fmt.Printf("Profile %s deleted successfully.\n", name)
	return nil
}

func RunToolAssociate(c *cli.Context) error {
	profileName := c.String("profile")
	toolName := c.String("tool")
	acl := c.String("acl")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	profile, err := queries.GetProfileByName(ctx, profileName)
	if err != nil {
		return fmt.Errorf("failed to get profile %s: %w", profileName, err)
	}

	tool, err := queries.GetMCPToolByName(ctx, toolName)
	if err != nil {
		return fmt.Errorf("failed to get tool %s: %w", toolName, err)
	}

	params := db.CreateProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool.ID,
		Acl:       acl,
	}

	_, err = queries.CreateProfileTool(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to associate tool %s with profile %s: %w", toolName, profileName, err)
	}

	fmt.Printf("Tool %s associated with profile %s with ACL %s successfully.\n", toolName, profileName, acl)
	return nil
}

func RunToolDisassociate(c *cli.Context) error {
	profileName := c.String("profile")
	toolName := c.String("tool")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	profile, err := queries.GetProfileByName(ctx, profileName)
	if err != nil {
		return fmt.Errorf("failed to get profile %s: %w", profileName, err)
	}

	tool, err := queries.GetMCPToolByName(ctx, toolName)
	if err != nil {
		return fmt.Errorf("failed to get tool %s: %w", toolName, err)
	}

	params := db.DeleteProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool.ID,
	}

	err = queries.DeleteProfileTool(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to disassociate tool %s from profile %s: %w", toolName, profileName, err)
	}

	fmt.Printf("Tool %s disassociated from profile %s successfully.\n", toolName, profileName)
	return nil
}

func RunToolList(c *cli.Context) error {
	profileName := c.String("profile")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	tools, err := queries.ListMCPToolsByProfile(ctx, profileName)
	if err != nil {
		return fmt.Errorf("failed to list tools for profile %s: %w", profileName, err)
	}

	if len(tools) == 0 {
		fmt.Printf("No tools found for profile %s.\n", profileName)
		return nil
	}

	fmt.Printf("Tools for profile %s:\n", profileName)
	for _, t := range tools {
		fmt.Printf("  ID: %d, Name: %s, Description: %s\n", t.ID, t.ToolName, t.Description.String)
	}
	return nil
}

func RunUserCreate(c *cli.Context) error {
	username := c.String("username")
	password := c.String("password")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// Hash the password (using a simple placeholder for now)
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	params := db.CreateUserParams{
		Username:    username,
		PasswordHash: string(passwordHash),
	}

	user, err := queries.CreateUser(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	fmt.Printf("User created successfully: ID=%d, Username=%s\n", user.ID, user.Username)
	return nil
}

func RunUserList(c *cli.Context) error {
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	users, err := queries.ListUsers(ctx)
	if err != nil {
		return fmt.Errorf("failed to list users: %w", err)
	}

	if len(users) == 0 {
		fmt.Println("No users found.")
		return nil
	}

	fmt.Println("Users:")
	for _, u := range users {
		fmt.Printf("  ID: %d, Username: %s\n", u.ID, u.Username)
	}
	return nil
}

func RunUserAssignRole(c *cli.Context) error {
	username := c.String("username")
	roleName := c.String("role")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	user, err := queries.GetUserByUsername(ctx, username)
	if err != nil {
		return fmt.Errorf("failed to get user %s: %w", username, err)
	}

	role, err := queries.GetRoleByName(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to get role %s: %w", roleName, err)
	}

	params := db.CreateUserRoleParams{
		UserID: user.ID,
		RoleID: role.ID,
	}

	_, err = queries.CreateUserRole(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to assign role %s to user %s: %w", roleName, username, err)
	}

	fmt.Printf("Role %s assigned to user %s successfully.\n", roleName, username)
	return nil
}

func RunUserRemoveRole(c *cli.Context) error {
	username := c.String("username")
	roleName := c.String("role")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	user, err := queries.GetUserByUsername(ctx, username)
	if err != nil {
		return fmt.Errorf("failed to get user %s: %w", username, err)
	}

	role, err := queries.GetRoleByName(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to get role %s: %w", roleName, err)
	}

	params := db.DeleteUserRoleParams{
		UserID: user.ID,
		RoleID: role.ID,
	}

	err = queries.DeleteUserRole(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to remove role %s from user %s: %w", roleName, username, err)
	}

	fmt.Printf("Role %s removed from user %s successfully.\n", roleName, username)
	return nil
}

func RunTUI(c *cli.Context) error {
	dbConnectionString := c.String("db")

	// Initialize the Bubble Tea program
	p := tea.NewProgram(tui.InitialModel(dbConnectionString))
	if _, err := p.Run(); err != nil {
		return fmt.Errorf("error running TUI: %w", err)
	}
	return nil
}
