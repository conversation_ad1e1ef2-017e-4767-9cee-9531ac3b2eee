.PHONY: test test-unit test-integration test-all build clean deps lint fmt vet test-e2e-server-initialize test-e2e-full-flow run-server-for-inspector stop-server-for-inspector

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=gofmt
GOVET=$(GOCMD) vet

# Build parameters
BINARY_NAME=sair
MAIN_PATH=./cmd/air

# Test parameters
TEST_TIMEOUT=10m
UNIT_TEST_PATTERN=./tests/unit/...
INTEGRATION_TEST_PATTERN=./tests/integration/...

# Default target
all: deps fmt vet test build

# Install dependencies
deps:
	$(GOMOD) download
	$(GOMOD) tidy
	@echo "Installing goose..."
	$(GOCMD) install github.com/pressly/goose/v3/cmd/goose@latest

# Build the application
build:
	$(GOBUILD) -o $(BINARY_NAME) $(MAIN_PATH)

# Clean build artifacts
clean:
	$(GOCMD) clean
	rm -f $(BINARY_NAME)

# Run all tests
test: test-unit test-integration

# Run unit tests only
test-unit:
	@echo "Running unit tests..."
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) $(UNIT_TEST_PATTERN)

# Run integration tests only (requires Docker or TEST_DATABASE_URL)
test-integration:
	@echo "Running integration tests..."
	@echo "Note: Integration tests require Docker or TEST_DATABASE_URL environment variable"
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) $(INTEGRATION_TEST_PATTERN)

# Run all tests with coverage
test-coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) -cover -coverprofile=coverage.out ./tests/...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Run only TestE2E_ServerInitialize
test-e2e-server-initialize:
	@echo "Running TestE2E_ServerInitialize..."
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) ./tests/integration -run TestE2E_ServerInitialize

# Run only TestE2E_FullFlow
test-e2e-full-flow:
	@echo "Running TestE2E_FullFlow..."
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) ./tests/integration -run TestE2E_FullFlow

# Run tests without Docker dependencies
test-no-docker:
	@echo "Running tests without Docker dependencies..."
	SKIP_DB_TESTS=true $(GOTEST) -v -timeout $(TEST_TIMEOUT) ./tests/...

# Format code
fmt:
	$(GOFMT) -s -w .

# Lint code
lint:
	golangci-lint run

# Vet code
vet:
	$(GOVET) ./...

# Generate code (sqlc)
generate:
	sqlc generate

# Run development database (requires Docker)
dev-db:
	@echo "Starting development PostgreSQL database..."
	docker run --rm -d \
		--name sair-dev-db \
		-p 5432:5432 \
		-e POSTGRES_USER=devuser \
		-e POSTGRES_PASSWORD=devpass \
		-e POSTGRES_DB=sairdev \
		postgres:13
	@echo "Database started at: postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable"
	@echo "Stop with: docker stop sair-dev-db"
	@sleep 2
	$(MAKE) migrate-dev # User needs to run migrations manually for now

# Stop development database
stop-dev-db:
	docker stop sair-dev-db

# Run server for MCP Inspector
run-server-for-inspector:
	@echo "Setting up and running server for MCP Inspector..."
	$(MAKE) dev-db
	$(MAKE) build
	@echo "Inserting default profile and tool..."
	$(GOBUILD) -o $(BINARY_NAME) ./cmd/air
	./$(BINARY_NAME) convert -f tests/testdata/openapi_specs/simple.json -db "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable"
	@echo "Starting MCP server..."
	./$(BINARY_NAME) serve -p 8080 -db "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable" &
	@echo "Server started on port 8080. Use http://localhost:8080/mcp/default-profile for Inspector."
	@echo "To stop the server, run 'make stop-server-for-inspector'"

stop-server-for-inspector:
	@echo "Stopping MCP server..."
	pkill -f "./$(BINARY_NAME) serve"
	$(MAKE) stop-dev-db

# Run database migrations
migrate-dev:
	@echo "Running database migrations with goose..."
	goose -dir ./sql/migrations postgres "postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable" up

# Help
help:
	@echo "Available targets:"
	@echo "  build           - Build the application binary"
	@echo "  test            - Run all tests (unit + integration)"
	@echo "  test-unit       - Run unit tests only"
	@echo "  test-integration - Run integration tests only"
	@echo "  test-coverage   - Run tests with coverage report"
	@echo "  test-no-docker  - Run tests without Docker dependencies"
	@echo "  deps            - Install dependencies"
	@echo "  fmt             - Format code"
	@echo "  lint            - Lint code (requires golangci-lint)"
	@echo "  vet             - Vet code"
	@echo "  generate        - Generate code using sqlc"
	@echo "  dev-db          - Start development database"
	@echo "  stop-dev-db     - Stop development database"
	@echo "  migrate-dev     - Show migration instructions"
	@echo "  clean           - Clean build artifacts"
	@echo "  help            - Show this help message"
	@echo ""
	@echo "Environment variables:"
	@echo "  TEST_DATABASE_URL - Use existing database for tests"
	@echo "  SKIP_DB_TESTS     - Skip database-dependent tests"