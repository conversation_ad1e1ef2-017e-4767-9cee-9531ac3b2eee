-- +goose Up
CREATE TABLE mcp_tools (
                           id BIGSERIAL PRIMARY KEY,
                           tool_name TEXT NOT NULL,
                           description TEXT,
                           input_schema JSONB NOT NULL,
                           created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                           updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_tool_name ON mcp_tools (tool_name);

COMMENT ON COLUMN mcp_tools.tool_name IS 'The unique name for the MCP tool, e.g., createUser.';
COMMENT ON COLUMN mcp_tools.description IS 'The description of what the tool does, taken from the OpenAPI summary.';
COMMENT ON COLUMN mcp_tools.input_schema IS 'The JSON schema for the tool''s input, combining all parameters.';


CREATE TABLE mcp_tool_mappings (
                                   id BIGSERIAL PRIMARY KEY,
                                   mcp_tool_id BIGINT NOT NULL REFERENCES mcp_tools(id) ON DELETE CASCADE,
                                   openapi_path TEXT NOT NULL,
                                   http_method TEXT NOT NULL,
                                   param_mappings JSONB,
                                   body_mapping JSONB,
                                   created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_mcp_tool_id ON mcp_tool_mappings (mcp_tool_id);

COMMENT ON COLUMN mcp_tool_mappings.mcp_tool_id IS 'Foreign key to the mcp_tools table.';
COMMENT ON COLUMN mcp_tool_mappings.openapi_path IS 'The original OpenAPI path template, e.g., /users/{userId}.';
COMMENT ON COLUMN mcp_tool_mappings.http_method IS 'The HTTP method for the endpoint (e.g., POST, GET).';
COMMENT ON COLUMN mcp_tool_mappings.param_mappings IS 'A JSON object mapping MCP properties back to their OpenAPI parameter sources (path, query, header).';
COMMENT ON COLUMN mcp_tool_mappings.body_mapping IS 'A JSON object describing how to reconstruct the original request body.';

-- +goose Down
DROP TABLE IF EXISTS mcp_tool_mappings CASCADE;
DROP TABLE IF EXISTS mcp_tools CASCADE;

