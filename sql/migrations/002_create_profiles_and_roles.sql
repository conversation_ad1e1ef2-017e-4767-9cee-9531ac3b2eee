-- +goose Up
-- Profiles are groups of MCP tools.
CREATE TABLE profiles (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    path_segment TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_profile_name ON profiles (name);
CREATE UNIQUE INDEX idx_profile_path_segment ON profiles (path_segment);

COMMENT ON COLUMN profiles.name IS 'The unique name for the profile, e.g., "billing_admin_tools".';
COMMENT ON COLUMN profiles.description IS 'A description of what this profile contains.';
COMMENT ON COLUMN profiles.path_segment IS 'A unique string for the URL path, e.g., "billing".';

-- Roles define user roles within the system.
CREATE TABLE roles (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_role_name ON roles (name);

COMMENT ON COLUMN roles.name IS 'The unique name for the role, e.g., "admin", "viewer".';

-- profile_tools links tools to profiles with an ACL.
CREATE TABLE profile_tools (
    profile_id BIGINT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    tool_id BIGINT NOT NULL REFERENCES mcp_tools(id) ON DELETE CASCADE,
    acl TEXT NOT NULL, -- e.g., 'EXECUTE', 'READ_ONLY', 'DENY'
    PRIMARY KEY (profile_id, tool_id)
);

COMMENT ON COLUMN profile_tools.acl IS 'Access control level for the tool in this profile.';

-- role_profiles links roles to profiles.
CREATE TABLE role_profiles (
    role_id BIGINT NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    profile_id BIGINT NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, profile_id)
);

COMMENT ON TABLE role_profiles IS 'Maps which roles have access to which profiles.';

-- +goose Down
DROP TABLE IF EXISTS role_profiles CASCADE;
DROP TABLE IF EXISTS profile_tools CASCADE;
DROP TABLE IF EXISTS roles CASCADE;
DROP TABLE IF EXISTS profiles CASCADE;
