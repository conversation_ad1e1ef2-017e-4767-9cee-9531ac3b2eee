package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/server"
)

// MockQueries implements db.Querier for testing
type MockQueries struct{}

func (m *MockQueries) CreateProfile(ctx context.Context, arg db.CreateProfileParams) (db.Profile, error) {
	return db.Profile{
		ID:          1,
		Name:        arg.Name,
		Description: arg.Description,
		PathSegment: arg.PathSegment,
		CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
	}, nil
}

func (m *MockQueries) ListProfiles(ctx context.Context) ([]db.Profile, error) {
	return []db.Profile{
		{
			ID:          1,
			Name:        "test-profile",
			Description: pgtype.Text{String: "Test profile for MCP Inspector", Valid: true},
			PathSegment: "test",
			CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
			UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		},
		{
			ID:          2,
			Name:        "demo-profile",
			Description: pgtype.Text{String: "Demo profile with sample tools", Valid: true},
			PathSegment: "demo",
			CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
			UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		},
	}, nil
}

func (m *MockQueries) GetProfileByID(ctx context.Context, id int64) (db.Profile, error) {
	return db.Profile{
		ID:          id,
		Name:        "test-profile",
		Description: pgtype.Text{String: "Test profile for MCP Inspector", Valid: true},
		PathSegment: "test",
		CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
	}, nil
}

func (m *MockQueries) UpdateProfile(ctx context.Context, arg db.UpdateProfileParams) (db.Profile, error) {
	return db.Profile{
		ID:          arg.ID,
		Name:        arg.Name,
		Description: arg.Description,
		PathSegment: arg.PathSegment,
		CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
	}, nil
}

func (m *MockQueries) DeleteProfile(ctx context.Context, id int64) error {
	return nil
}

func (m *MockQueries) CreateMCPTool(ctx context.Context, arg db.CreateMCPToolParams) (db.McpTool, error) {
	return db.McpTool{
		ID:          1,
		ToolName:    arg.ToolName,
		Description: arg.Description,
		InputSchema: arg.InputSchema,
		CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
	}, nil
}

func (m *MockQueries) ListMCPTools(ctx context.Context) ([]db.McpTool, error) {
	return []db.McpTool{
		{
			ID:          1,
			ToolName:    "echo",
			Description: pgtype.Text{String: "Echo back the input message", Valid: true},
			InputSchema: []byte(`{"type":"object","properties":{"message":{"type":"string","description":"Message to echo back"}},"required":["message"]}`),
			CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
			UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		},
		{
			ID:          2,
			ToolName:    "get-time",
			Description: pgtype.Text{String: "Get the current time", Valid: true},
			InputSchema: []byte(`{"type":"object","properties":{}}`),
			CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
			UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		},
	}, nil
}

func (m *MockQueries) CreateMCPToolMapping(ctx context.Context, arg db.CreateMCPToolMappingParams) (db.McpToolMapping, error) {
	return db.McpToolMapping{
		ID:            1,
		McpToolID:     arg.McpToolID,
		OpenapiPath:   arg.OpenapiPath,
		HttpMethod:    arg.HttpMethod,
		ParamMappings: arg.ParamMappings,
		BodyMapping:   arg.BodyMapping,
		CreatedAt:     pgtype.Timestamptz{Time: time.Now(), Valid: true},
	}, nil
}

func (m *MockQueries) GetToolWithMapping(ctx context.Context, toolName string) (db.GetToolWithMappingRow, error) {
	return db.GetToolWithMappingRow{
		ToolID:        1,
		ToolName:      toolName,
		Description:   pgtype.Text{String: "Test tool", Valid: true},
		InputSchema:   []byte(`{"type":"object","properties":{"message":{"type":"string"}}}`),
		MappingID:     1,
		OpenapiPath:   "/test",
		HttpMethod:    "POST",
		ParamMappings: []byte(`{}`),
		BodyMapping:   []byte(`{}`),
	}, nil
}

func (m *MockQueries) CreateRole(ctx context.Context, name string) (db.Role, error) {
	return db.Role{
		ID:        1,
		Name:      name,
		CreatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
		UpdatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
	}, nil
}

func main() {
	port := 8081
	if len(os.Args) > 1 {
		if p, err := strconv.Atoi(os.Args[1]); err == nil {
			port = p
		}
	}

	log.Printf("Starting SUSE AIR MCP Test Server on port %d", port)
	log.Printf("This is a test server with mock data for MCP Inspector testing")
	
	// Create mock database
	mockQueries := &MockQueries{}
	
	// Create MCP server
	mcpServer := server.NewServer(mockQueries)
	
	// Set up routes
	profileRouter := server.ProfileRouter(mcpServer)
	
	// Add CORS headers for MCP Inspector
	corsHandler := func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Origin", "*")
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
			
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}
			
			next.ServeHTTP(w, r)
		})
	}
	
	http.Handle("/mcp/", corsHandler(profileRouter))
	
	// Add a health check endpoint
	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status":"ok","message":"SUSE AIR MCP Test Server"}`))
	})
	
	log.Printf("Server ready! Test with MCP Inspector:")
	log.Printf("  npx @modelcontextprotocol/inspector http://localhost:%d/mcp/test", port)
	log.Printf("  npx @modelcontextprotocol/inspector http://localhost:%d/mcp/demo", port)
	
	log.Fatal(http.ListenAndServe(fmt.Sprintf(":%d", port), nil))
}
