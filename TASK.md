# Task List - SUSE AI Runtime

## Overview
This document tracks all tasks for building the SUSE AIR platform. Tasks are organized by phase and component.

## Phase 0: Setup
### Write Tests for existing code
- [X] Write tests for existing code

## Phase 1: Core MCP Server & Profile Management

This phase focuses on setting up the database and the basic server structure that can handle MCP connections and route them to the correct profile.

- [X] **Data Model:**
  - [X] Design and create database migration for `profiles` table.
  - [X] Design and create database migration for `roles` table.
  - [X] Design and create database migration for `profile_tools` join table (linking profiles to tools with ACLs).
  - [X] Design and create database migration for `role_profiles` join table (linking roles to profiles).
  - [X] Update database queries and models (`sqlc`).
- [X] **MCP Server Foundation:**
  - [X] Implement the standard MCP connection lifecycle (`initialize`, `initialized`, `shutdown`).
  - [X] Implement the Streamable HTTP transport for JSON-RPC messages.
  - [X] Create a **Profile Router** that maps requests from a URL path (e.g., `/{profile_path}`) to the corresponding profile configuration.

## Phase 2: MCP Features & Authorization

This phase implements the core logic for exposing tools and securing the server according to the MCP specification.

- [X] **MCP `tools` Feature Implementation:**
  - [X] Implement the `tools/list` request handler. It must query the DB based on the profile and user's role/ACLs.
  - [X] Implement the `tools/call` request handler. It must perform an ACL check before delegating to the schema executor.
  - [X] Ensure tool definitions, requests, and results conform to the MCP JSON schema.
- [X] **MCP-Compliant Authorization (OAuth 2.1):**
  - [X] Implement `401 Unauthorized` responses with the `WWW-Authenticate` header pointing to resource metadata.
  - [X] Expose OAuth 2.0 Protected Resource Metadata (`/.well-known/oauth-protected-resource`).
  - [X] Implement an Authorization Server that exposes its own metadata (`/.well-known/oauth-authorization-server`) and supports the Authorization Code Grant with PKCE.
  - [ ] Ensure the MCP Server (as a Resource Server) validates access tokens, including the audience (`aud`) claim.

## Phase 3: Integration & Testing

## CLI Implementation
- [X] Implement CLI using `urfave/cli` for `convert`, `execute`, and `serve` commands.
- [X] Make sure all unit tests pass before continue.
- [X] Make sure all integration tests pass before continue.
- [X] Integrate all components for the complete, end-to-end request lifecycle.
- [X] Write integration tests for the full flow: auth -> routing -> ACL check -> execution.
- [ ] Test the server's compliance and functionality using the official **MCP Inspector** tool.

## Phase 4: Profile & User Management CLI/TUI

This phase focuses on building a command-line interface (CLI) and a Text User Interface (TUI) for managing profiles, tools, and users within the SUSE AIR system.

- [ ] **Data Model Enhancements:**
  - [ ] Review existing `profiles`, `roles`, `profile_tools`, and `role_profiles` tables.
  - [ ] Determine if additional tables or fields are required for explicit user management (e.g., `users` table, `user_roles` join table).
  - [ ] Update database queries and models (`sqlc`) for any new schema.
- [ ] **CLI Commands (`urfave/cli`):**
  - [ ] Implement `sair profile <command>`:
    - [ ] `sair profile create <name> [description]`
    - [ ] `sair profile list`
    - [ ] `sair profile get <name>`
    - [ ] `sair profile update <name> [new_name] [new_description]`
    - [ ] `sair profile delete <name>`
  - [ ] Implement `sair tool <command>`:
    - [ ] `sair tool associate <profile_name> <tool_id> [acl_rules]`
    - [ ] `sair tool disassociate <profile_name> <tool_id>`
    - [ ] `sair tool list <profile_name>` (list tools associated with a profile)
  - [ ] Implement `sair user <command>` (if user management is added):
    - [ ] `sair user create <username> [password]`
    - [ ] `sair user list`
    - [ ] `sair user assign-role <username> <role_name>`
    - [ ] `sair user remove-role <username> <role_name>`
- [X] **TUI Implementation (`charm.sh/bubbletea`):**
  - [X] Create a new `sair tui` command to launch the interactive TUI.
  - [X] Design a main menu for navigating between "Profile Management", "Tool Association", and "User Management" (if applicable).
  - [X] Implement interactive screens for:
    - [X] Listing, creating, viewing details, updating, and deleting profiles.
    - [X] Associating/disassociating tools with profiles.
    - [X] (If applicable) User creation, listing, and role assignment.
  - [ ] Ensure clear user feedback for operations (success/failure messages).
- [ ] **Integration:**
  - [ ] Ensure CLI commands and TUI components correctly interact with the `internal/db` layer.
  - [ ] Leverage existing `internal/models` for data structures.
- [ ] **Testing:**
  - [ ] Write unit tests for new CLI command logic.
  - [ ] Write unit tests for TUI components (model updates, view rendering).
  - [ ] Write integration tests to verify end-to-end functionality of CLI and TUI operations against the database.