---
title: "Example Clients"
description: "A list of applications that support MCP integrations"
---

This page provides an overview of applications that support the Model Context Protocol (MCP). Each client may support different MCP features, allowing for varying levels of integration with MCP servers.

## Feature support matrix

<div id="feature-support-matrix-wrapper">

{/* prettier-ignore-start */}

| Client                                           | [Resources] | [Prompts] | [Tools] | [Discovery] | [Sampling] | [Roots] | Notes                                                                                           |
| ------------------------------------------------ | ----------- | --------- | ------- | ---------------------- | ---------- | ----- | ----------------------------------------------------------------------------------------------- |
| [5ire][5ire]                                     | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools.                                                                                 |
| [AgentAI][AgentAI]                               | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Agent Library written in Rust with tools support                                                |
| [AgenticFlow][AgenticFlow]                       | ✅          | ✅        | ✅      | ✅                     | ❌         | ❌    | Supports tools, prompts, and resources for no-code AI agents and multi-agent workflows.         |
| [Amazon Q CLI][Amazon Q CLI]                     | ❌          | ✅        | ✅      | ❓                     | ❌         | ❌    | Supports prompts and tools.                                                                     |
| [Apify MCP Tester][Apify MCP Tester]             | ❌          | ❌        | ✅      | ✅                     | ❌         | ❌    | Supports remote MCP servers and tool discovery.                                                 |
| [Augment Code][AugmentCode]                      | ❌          | ❌        | ✅      | ❌                     | ❌         | ❌    | Supports tools in local and remote agents.                                                                                 |
| [BeeAI Framework][BeeAI Framework]               | ❌          | ❌        | ✅      | ❌                     | ❌         | ❌    | Supports tools in agentic workflows.                                                            |
| [BoltAI][BoltAI]                                 | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools.                                                                                 |
| [Claude.ai][Claude.ai]                           | ✅          | ✅        | ✅      | ❌                     | ❌         | ❌    | Supports tools, prompts, and resources for remote MCP servers.                                  |
| [Claude Code][Claude Code]                       | ❌          | ✅        | ✅      | ❌                     | ❌         | ❌    | Supports prompts and tools                                                                      |
| [Claude Desktop App][Claude Desktop]             | ✅          | ✅        | ✅      | ❌                     | ❌         | ❌    | Supports tools, prompts, and resources for local and remote MCP servers.                        |
| [Cline][Cline]                                   | ✅          | ❌        | ✅      | ✅                     | ❌         | ❌    | Supports tools and resources.                                                                   |
| [Continue][Continue]                             | ✅          | ✅        | ✅      | ❓                     | ❌         | ❌    | Supports tools, prompts, and resources.                                                         |
| [Copilot-MCP][CopilotMCP]                        | ✅          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools and resources.                                                                   |
| [Cursor][Cursor]                                 | ❌          | ❌        | ✅      | ❌                     | ❌         | ❌    | Supports tools.                                                                                 |
| [Daydreams Agents][Daydreams]                    | ✅          | ✅        | ✅      | ❌                     | ❌         | ❌    | Support for drop in Servers to Daydreams agents                                                 |
| [Emacs Mcp][Mcp.el]                              | ❌          | ❌        | ✅      | ❌                     | ❌         | ❌    | Supports tools in Emacs.                                                                        |
| [fast-agent][fast-agent]                         | ✅          | ✅        | ✅      | ✅                     | ✅         | ✅    | Full multimodal MCP support, with end-to-end tests                                              |
| [FLUJO][FLUJO]                                   | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Support for resources, Prompts and Roots are coming soon                                        |
| [Genkit][Genkit]                                 | ⚠️          | ✅        | ✅      | ❓                     | ❌         | ❌    | Supports resource list and lookup through tools.                                                |
| [Glama][Glama]                                   | ✅          | ✅        | ✅      | ❓                     | ❌         | ❌    | Supports tools.                                                                                 |
| [GenAIScript][GenAIScript]                       | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools.                                                                                 |
| [Goose][Goose]                                   | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools.                                                                                 |
| [gptme][gptme]                                   | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools.                                                                                 |
| [HyperAgent][HyperAgent]                         | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools.                                                                                 |
| [JetBrains AI Assistant][JetBrains AI Assistant] | ❌          | ❌        | ✅      | ❌                     | ❌         | ❌    | Supports tools for all JetBrains IDEs.                                                          |
| [Klavis AI Slack/Discord/Web][Klavis AI]         | ✅          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools and resources.                                                                   |
| [LibreChat][LibreChat]                           | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools for Agents                                                                       |
| [Lutra][Lutra]                                   | ✅          | ✅        | ✅      | ❓                     | ❌         | ❌    | Supports any MCP server for reusable playbook creation.                                         |
| [mcp-agent][mcp-agent]                           | ✅          | ✅        | ✅      | ❓                     | ⚠️         | ✅    | Supports tools, prompts, resources, roots, server connection management, and agent workflows.   |
| [mcp-use][mcp-use]                               | ✅          | ✅        | ✅      | ❓                     | ❌         | ❌    | Support tools, resources, stdio & http connection, local llms-agents.                           |
| [MCPHub][MCPHub]                                 | ✅          | ✅        | ✅      | ❓                     | ❌         | ❌    | Supports tools, resources, and prompts in Neovim                                                |
| [MCPOmni-Connect][MCPOmni-Connect]               | ✅          | ✅        | ✅      | ❓                     | ✅         | ❌    | Supports tools with agentic mode, ReAct, and orchestrator capabilities.                         |
| [Memex][Memex]                                   | ✅          | ✅        | ✅      | ❓                     | ❌         | ❌    | Support tools. Also support *building and testing* MCP server, all-in-one desktop app.          |
| [Microsoft Copilot Studio]                       | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools                                                                                  |
| [MindPal][MindPal]                               | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools for no-code AI agents and multi-agent workflows.                                 |
| [MooPoint][MooPoint]                             | ❌          | ❌        | ✅      | ❓                     | ✅         | ❌    | Web-Hosted client with tool calling support                                 |
| [Msty Studio][Msty Studio]                       | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools                                                                                  |
| [NVIDIA Agent Intelligence toolkit][AIQ toolkit] | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools in agentic workflows.                                                            |
| [OpenSumi][OpenSumi]                             | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools in OpenSumi                                                                      |
| [oterm][oterm]                                   | ❌          | ✅        | ✅      | ❓                     | ✅         | ❌    | Supports tools, prompts and sampling for Ollama.                                                |
| [Postman][postman]                               | ✅          | ✅        | ✅      | ❓                     | ❌         | ❌    | Supports tools, resources, prompts, and sampling                                                |
| [Roo Code][Roo Code]                             | ✅          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools and resources.                                                                   |
| [Slack MCP Client][Slack MCP Client]             | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools and multiple servers.                                                            |
| [Sourcegraph Cody][Cody]                         | ✅          | ❌        | ❌      | ❓                     | ❌         | ❌    | Supports resources through OpenCTX                                                              |
| [SpinAI][SpinAI]                                 | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools for Typescript AI Agents                                                         |
| [Superinterface][Superinterface]                 | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools                                                                                  |
| [Superjoin][Superjoin]                           | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools and multiple servers.                                                            |
| [TheiaAI/TheiaIDE][TheiaAI/TheiaIDE]             | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools for Agents in Theia AI and the AI-powered Theia IDE                              |
| [Tome][Tome]                                     | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools, manages MCP servers.                                                            |
| [TypingMind App][TypingMind App]                 | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools at app-level (appear as plugins) or when assigned to Agents                      |
| [VS Code GitHub Copilot][VS Code]                | ✅          | ✅        | ✅      | ✅                     | ✅         | ✅    | Supports dynamic tool/roots discovery, secure secret configuration, and explicit tool prompting |
| [Warp][Warp]                                     | ✅          | ❌        | ✅      | ✅                     | ❌         | ❌    | Supports tools, resources, and most of the discovery criteria                                   |
| [WhatsMCP][WhatsMCP]                             | ❌          | ❌        | ✅      | ❌                     | ❌         | ❌    | Supports tools for Remote MCP Servers in WhatsApp                                               |
| [Windsurf Editor][Windsurf]                      | ❌          | ❌        | ✅      | ✅                     | ❌         | ❌    | Supports tools with AI Flow for collaborative development.                                      |
| [Witsy][Witsy]                                   | ❌          | ❌        | ✅      | ❓                     | ❌         | ❌    | Supports tools in Witsy.                                                                        |
| [Zed][Zed]                                       | ❌          | ✅        | ❌      | ❌                     | ❌         | ❌    | Prompts appear as slash commands                                                                |
| [Zencoder][Zencoder]                             | ❌          | ❌        | ✅      | ❌                     | ❌         | ❌    | Supports tools                                                                                  |

{/* prettier-ignore-end */}

[Resources]: /docs/concepts/resources
[Prompts]: /docs/concepts/prompts
[Tools]: /docs/concepts/tools
[Discovery]: /docs/concepts/tools#tool-discovery-and-updates
[Sampling]: /docs/concepts/sampling
[Roots]: /docs/concepts/roots
[5ire]: https://github.com/nanbingxyz/5ire
[AgentAI]: https://github.com/AdamStrojek/rust-agentai
[AgenticFlow]: https://agenticflow.ai/mcp
[AIQ toolkit]: https://github.com/NVIDIA/AIQToolkit
[Amazon Q CLI]: https://github.com/aws/amazon-q-developer-cli
[Apify MCP Tester]: https://apify.com/jiri.spilka/tester-mcp-client
[AugmentCode]: https://augmentcode.com
[BeeAI Framework]: https://i-am-bee.github.io/beeai-framework
[BoltAI]: https://boltai.com
[Claude.ai]: https://claude.ai
[Claude Code]: https://claude.ai/code
[Claude Desktop]: https://claude.ai/download
[Cline]: https://github.com/cline/cline
[Continue]: https://github.com/continuedev/continue
[CopilotMCP]: https://github.com/VikashLoomba/copilot-mcp
[Cursor]: https://cursor.com
[Daydreams]: https://github.com/daydreamsai/daydreams
[Klavis AI]: https://www.klavis.ai/
[Mcp.el]: https://github.com/lizqwerscott/mcp.el
[fast-agent]: https://github.com/evalstate/fast-agent
[FLUJO]: https://github.com/mario-andreschak/flujo
[Glama]: https://glama.ai/chat
[Genkit]: https://github.com/firebase/genkit
[GenAIScript]: https://microsoft.github.io/genaiscript/reference/scripts/mcp-tools/
[Goose]: https://block.github.io/goose/docs/goose-architecture/#interoperability-with-extensions
[JetBrains AI Assistant]: https://plugins.jetbrains.com/plugin/22282-jetbrains-ai-assistant
[LibreChat]: https://github.com/danny-avila/LibreChat
[Lutra]: https://lutra.ai
[mcp-agent]: https://github.com/lastmile-ai/mcp-agent
[mcp-use]: https://github.com/pietrozullo/mcp-use
[MCPHub]: https://github.com/ravitemer/mcphub.nvim
[MCPOmni-Connect]: https://github.com/Abiorh001/mcp_omni_connect
[Memex]: https://memex.tech/
[Microsoft Copilot Studio]: https://learn.microsoft.com/en-us/microsoft-copilot-studio/agent-extend-action-mcp
[MindPal]: https://mindpal.io
[MooPoint]: https://moopoint.io
[Msty Studio]: https://msty.ai
[OpenSumi]: https://github.com/opensumi/core
[oterm]: https://github.com/ggozad/oterm
[Postman]: https://postman.com/downloads
[Roo Code]: https://roocode.com
[Slack MCP Client]: https://github.com/tuannvm/slack-mcp-client
[Cody]: https://sourcegraph.com/cody
[SpinAI]: https://spinai.dev
[Superinterface]: https://superinterface.ai
[Superjoin]: https://superjoin.ai
[TheiaAI/TheiaIDE]: https://eclipsesource.com/blogs/2024/12/19/theia-ide-and-theia-ai-support-mcp/
[Tome]: https://github.com/runebookai/tome
[TypingMind App]: https://www.typingmind.com
[VS Code]: https://code.visualstudio.com/
[Windsurf]: https://codeium.com/windsurf
[gptme]: https://github.com/gptme/gptme
[Warp]: https://www.warp.dev/
[WhatsMCP]: https://wassist.app/mcp/
[Witsy]: https://github.com/nbonamy/witsy
[Zed]: https://zed.dev
[Zencoder]: https://zencoder.ai
[HyperAgent]: https://github.com/hyperbrowserai/HyperAgent

</div>

## Client details

### 5ire

[5ire](https://github.com/nanbingxyz/5ire) is an open source cross-platform desktop AI assistant that supports tools through MCP servers.

**Key features:**

- Built-in MCP servers can be quickly enabled and disabled.
- Users can add more servers by modifying the configuration file.
- It is open-source and user-friendly, suitable for beginners.
- Future support for MCP will be continuously improved.

### AgentAI

[AgentAI](https://github.com/AdamStrojek/rust-agentai) is a Rust library designed to simplify the creation of AI agents. The library includes seamless integration with MCP Servers.

[Example of MCP Server integration](https://github.com/AdamStrojek/rust-agentai/blob/master/examples/tools_mcp.rs)

**Key features:**

- Multi-LLM – We support most LLM APIs (OpenAI, Anthropic, Gemini, Ollama, and all OpenAI API Compatible).
- Built-in support for MCP Servers.
- Create agentic flows in a type- and memory-safe language like Rust.

### AgenticFlow

[AgenticFlow](https://agenticflow.ai/) is a no-code AI platform that helps you build agents that handle sales, marketing, and creative tasks around the clock. Connect 2,500+ APIs and 10,000+ tools securely via MCP.

**Key features:**

- No-code AI agent creation and workflow building.
- Access a vast library of 10,000+ tools and 2,500+ APIs through MCP.
- Simple 3-step process to connect MCP servers.
- Securely manage connections and revoke access anytime.

**Learn more:**

- [AgenticFlow MCP Integration](https://agenticflow.ai/mcp)

### Amazon Q CLI

[Amazon Q CLI](https://github.com/aws/amazon-q-developer-cli) is an open-source, agentic coding assistant for terminals.

**Key features:**

- Full support for MCP servers.
- Edit prompts using your preferred text editor.
- Access saved prompts instantly with `@`.
- Control and organize AWS resources directly from your terminal.
- Tools, profiles, context management, auto-compact, and so much more!

**Get Started**

```bash
brew install amazon-q
```

### Apify MCP Tester

[Apify MCP Tester](https://github.com/apify/tester-mcp-client) is an open-source client that connects to any MCP server using Server-Sent Events (SSE).
It is a standalone Apify Actor designed for testing MCP servers over SSE, with support for Authorization headers.
It uses plain JavaScript (old-school style) and is hosted on Apify, allowing you to run it without any setup.

**Key features:**

- Connects to any MCP server via SSE.
- Works with the [Apify MCP Server](https://apify.com/apify/actors-mcp-server) to interact with one or more Apify [Actors](https://apify.com/store).
- Dynamically utilizes tools based on context and user queries (if supported by the server).

### Augment Code

[Augment Code](https://augmentcode.com) is an AI-powered coding platform for VS Code and JetBrains with autonomous agents, chat, and completions. Both local and remote agents are backed by full codebase awareness and native support for MCP, enabling enhanced context through external sources and tools.

**Key features:**

- Full MCP support in local and remote agents.
- Add additional context through MCP servers.
- Automate your development workflows with MCP tools.
- Works in VS Code and JetBrains IDEs.

### BeeAI Framework

[BeeAI Framework](https://i-am-bee.github.io/beeai-framework) is an open-source framework for building, deploying, and serving powerful agentic workflows at scale. The framework includes the **MCP Tool**, a native feature that simplifies the integration of MCP servers into agentic workflows.

**Key features:**

- Seamlessly incorporate MCP tools into agentic workflows.
- Quickly instantiate framework-native tools from connected MCP client(s).
- Planned future support for agentic MCP capabilities.

**Learn more:**

- [Example of using MCP tools in agentic workflow](https://i-am-bee.github.io/beeai-framework/#/typescript/tools?id=using-the-mcptool-class)

### BoltAI

[BoltAI](https://boltai.com) is a native, all-in-one AI chat client with MCP support. BoltAI supports multiple AI providers (OpenAI, Anthropic, Google AI...), including local AI models (via Ollama, LM Studio or LMX)

**Key features:**

- MCP Tool integrations: once configured, user can enable individual MCP server in each chat
- MCP quick setup: import configuration from Claude Desktop app or Cursor editor
- Invoke MCP tools inside any app with AI Command feature
- Integrate with remote MCP servers in the mobile app

**Learn more:**

- [BoltAI docs](https://boltai.com/docs/plugins/mcp-servers)
- [BoltAI website](https://boltai.com)

### Claude Code

Claude Code is an interactive agentic coding tool from Anthropic that helps you code faster through natural language commands. It supports MCP integration for prompts and tools, and also functions as an MCP server to integrate with other clients.

**Key features:**

- Tool and prompt support for MCP servers
- Offers its own tools through an MCP server for integrating with other MCP clients

### Claude.ai

[Claude.ai](https://claude.ai) is Anthropic's web-based AI assistant that provides MCP support for remote servers.

**Key features:**

- Support for remote MCP servers via integrations UI in settings
- Access to tools, prompts, and resources from configured MCP servers
- Seamless integration with Claude's conversational interface
- Enterprise-grade security and compliance features

### Claude Desktop App

The Claude desktop application provides comprehensive support for MCP, enabling deep integration with local tools and data sources.

**Key features:**

- Full support for resources, allowing attachment of local files and data
- Support for prompt templates
- Tool integration for executing commands and scripts
- Local server connections for enhanced privacy and security

### Cline

[Cline](https://github.com/cline/cline) is an autonomous coding agent in VS Code that edits files, runs commands, uses a browser, and more–with your permission at each step.

**Key features:**

- Create and add tools through natural language (e.g. "add a tool that searches the web")
- Share custom MCP servers Cline creates with others via the `~/Documents/Cline/MCP` directory
- Displays configured MCP servers along with their tools, resources, and any error logs

### Continue

[Continue](https://github.com/continuedev/continue) is an open-source AI code assistant, with built-in support for all MCP features.

**Key features:**

- Type "@" to mention MCP resources
- Prompt templates surface as slash commands
- Use both built-in and MCP tools directly in chat
- Supports VS Code and JetBrains IDEs, with any LLM

### Copilot-MCP

[Copilot-MCP](https://github.com/VikashLoomba/copilot-mcp) enables AI coding assistance via MCP.

**Key features:**

- Support for MCP tools and resources
- Integration with development workflows
- Extensible AI capabilities

### Cursor

[Cursor](https://docs.cursor.com/advanced/model-context-protocol) is an AI code editor.

**Key features:**

- Support for MCP tools in Cursor Composer
- Support for both STDIO and SSE

### Daydreams

[Daydreams](https://github.com/daydreamsai/daydreams) is a generative agent framework for executing anything onchain

**Key features:**

- Supports MCP Servers in config
- Exposes MCP Client

### Emacs Mcp

[Emacs Mcp](https://github.com/lizqwerscott/mcp.el) is an Emacs client designed to interface with MCP servers, enabling seamless connections and interactions. It provides MCP tool invocation support for AI plugins like [gptel](https://github.com/karthink/gptel) and [llm](https://github.com/ahyatt/llm), adhering to Emacs' standard tool invocation format. This integration enhances the functionality of AI tools within the Emacs ecosystem.

**Key features:**

- Provides MCP tool support for Emacs.

### fast-agent

[fast-agent](https://github.com/evalstate/fast-agent) is a Python Agent framework, with simple declarative support for creating Agents and Workflows, with full multi-modal support for Anthropic and OpenAI models.

**Key features:**

- PDF and Image support, based on MCP Native types
- Interactive front-end to develop and diagnose Agent applications, including passthrough and playback simulators
- Built in support for "Building Effective Agents" workflows.
- Deploy Agents as MCP Servers

### FLUJO

Think n8n + ChatGPT. FLUJO is an desktop application that integrates with MCP to provide a workflow-builder interface for AI interactions. Built with Next.js and React, it supports both online and offline (ollama) models, it manages API Keys and environment variables centrally and can install MCP Servers from GitHub. FLUJO has an ChatCompletions endpoint and flows can be executed from other AI applications like Cline, Roo or Claude.

**Key features:**

- Environment & API Key Management
- Model Management
- MCP Server Integration
- Workflow Orchestration
- Chat Interface

### Genkit

[Genkit](https://github.com/firebase/genkit) is a cross-language SDK for building and integrating GenAI features into applications. The [genkitx-mcp](https://github.com/firebase/genkit/tree/main/js/plugins/mcp) plugin enables consuming MCP servers as a client or creating MCP servers from Genkit tools and prompts.

**Key features:**

- Client support for tools and prompts (resources partially supported)
- Rich discovery with support in Genkit's Dev UI playground
- Seamless interoperability with Genkit's existing tools and prompts
- Works across a wide variety of GenAI models from top providers

### Glama

[Glama](https://glama.ai/chat) is a comprehensive AI workspace and integration platform that offers a unified interface to leading LLM providers, including OpenAI, Anthropic, and others. It supports the Model Context Protocol (MCP) ecosystem, enabling developers and enterprises to easily discover, build, and manage MCP servers.

**Key features:**

- Integrated [MCP Server Directory](https://glama.ai/mcp/servers)
- Integrated [MCP Tool Directory](https://glama.ai/mcp/tools)
- Host MCP servers and access them via the Chat or SSE endpoints
  – Ability to chat with multiple LLMs and MCP servers at once
- Upload and analyze local files and data
- Full-text search across all your chats and data

### GenAIScript

Programmatically assemble prompts for LLMs using [GenAIScript](https://microsoft.github.io/genaiscript/) (in JavaScript). Orchestrate LLMs, tools, and data in JavaScript.

**Key features:**

- JavaScript toolbox to work with prompts
- Abstraction to make it easy and productive
- Seamless Visual Studio Code integration

### Goose

[Goose](https://github.com/block/goose) is an open source AI agent that supercharges your software development by automating coding tasks.

**Key features:**

- Expose MCP functionality to Goose through tools.
- MCPs can be installed directly via the [extensions directory](https://block.github.io/goose/v1/extensions/), CLI, or UI.
- Goose allows you to extend its functionality by [building your own MCP servers](https://block.github.io/goose/docs/tutorials/custom-extensions).
- Includes built-in tools for development, web scraping, automation, memory, and integrations with JetBrains and Google Drive.

### gptme

[gptme](https://github.com/gptme/gptme) is a open-source terminal-based personal AI assistant/agent, designed to assist with programming tasks and general knowledge work.

**Key features:**

- CLI-first design with a focus on simplicity and ease of use
- Rich set of built-in tools for shell commands, Python execution, file operations, and web browsing
- Local-first approach with support for multiple LLM providers
- Open-source, built to be extensible and easy to modify

### HyperAgent

[HyperAgent](https://github.com/hyperbrowserai/HyperAgent) is Playwright supercharged with AI. With HyperAgent, you no longer need brittle scripts, just powerful natural language commands. Using MCP servers, you can extend the capability of HyperAgent, without having to write any code.

**Key features:**

- AI Commands: Simple APIs like page.ai(), page.extract() and executeTask() for any AI automation
- Fallback to Regular Playwright: Use regular Playwright when AI isn't needed
- Stealth Mode – Avoid detection with built-in anti-bot patches
- Cloud Ready – Instantly scale to hundreds of sessions via [Hyperbrowser](https://www.hyperbrowser.ai/)
- MCP Client – Connect to tools like Composio for full workflows (e.g. writing web data to Google Sheets)

### JetBrains AI Assistant

[JetBrains AI Assistant](https://plugins.jetbrains.com/plugin/22282-jetbrains-ai-assistant) plugin provides AI-powered features for software development available in all JetBrains IDEs.

**Key features:**

- Unlimited code completion powered by Mellum, JetBrains’ proprietary AI model.
- Context-aware AI chat that understands your code and helps you in real time.
- Access to top-tier models from OpenAI, Anthropic, and Google.
- Offline mode with connected local LLMs via Ollama or LM Studio.
- Deep integration into IDE workflows, including code suggestions in the editor, VCS assistance, runtime error explanation, and more.

### Klavis AI Slack/Discord/Web

[Klavis AI](https://www.klavis.ai/) is an Open-Source Infra to Use, Build & Scale MCPs with ease.

**Key features:**

- Slack/Discord/Web MCP clients for using MCPs directly
- Simple web UI dashboard for easy MCP configuration
- Direct OAuth integration with Slack & Discord Clients and MCP Servers for secure user authentication
- SSE transport support
- Open-source infrastructure ([GitHub repository](https://github.com/Klavis-AI/klavis))

**Learn more:**

- [Demo video showing MCP usage in Slack/Discord](https://youtu.be/9-QQAhrQWw8)

### LibreChat

[LibreChat](https://github.com/danny-avila/LibreChat) is an open-source, customizable AI chat UI that supports multiple AI providers, now including MCP integration.

**Key features:**

- Extend current tool ecosystem, including [Code Interpreter](https://www.librechat.ai/docs/features/code_interpreter) and Image generation tools, through MCP servers
- Add tools to customizable [Agents](https://www.librechat.ai/docs/features/agents), using a variety of LLMs from top providers
- Open-source and self-hostable, with secure multi-user support
- Future roadmap includes expanded MCP feature support

### Lutra

[Lutra](https://lutra.ai) is an AI agent that transforms conversations into actionable, automated workflows.

**Key features:**

- Easy MCP Integration: Connecting Lutra to MCP servers is as simple as providing the server URL; Lutra handles the rest behind the scenes.
- Chat to Take Action: Lutra understands your conversational context and goals, automatically integrating with your existing apps to perform tasks.
- Reusable Playbooks: After completing a task, save the steps as reusable, automated workflows—simplifying repeatable processes and reducing manual effort.
- Shareable Automations: Easily share your saved playbooks with teammates to standardize best practices and accelerate collaborative workflows.

**Learn more:**

- [Lutra AI agent explained](https://www.youtube.com/watch?v=W5ZpN0cMY70)

### mcp-agent

[mcp-agent] is a simple, composable framework to build agents using Model Context Protocol.

**Key features:**

- Automatic connection management of MCP servers.
- Expose tools from multiple servers to an LLM.
- Implements every pattern defined in [Building Effective Agents](https://www.anthropic.com/research/building-effective-agents).
- Supports workflow pause/resume signals, such as waiting for human feedback.

### mcp-use

[mcp-use] is an open source python library to very easily connect any LLM to any MCP server both locally and remotely.

**Key features:**

- Very simple interface to connect any LLM to any MCP.
- Support the creation of custom agents, workflows.
- Supports connection to multiple MCP servers simultaneously.
- Supports all langchain supported models, also locally.
- Offers efficient tool orchestration and search functionalities.

### MCPHub

[MCPHub] is a powerful Neovim plugin that integrates MCP (Model Context Protocol) servers into your workflow.

**Key features:**

- Install, configure and manage MCP servers with an intuitive UI.
- Built-in Neovim MCP server with support for file operations (read, write, search, replace), command execution, terminal integration, LSP integration, buffers, and diagnostics.
- Create Lua-based MCP servers directly in Neovim.
- Inegrates with popular Neovim chat plugins Avante.nvim and CodeCompanion.nvim

### MCPOmni-Connect

[MCPOmni-Connect](https://github.com/Abiorh001/mcp_omni_connect) is a versatile command-line interface (CLI) client designed to connect to various Model Context Protocol (MCP) servers using both stdio and SSE transport.

**Key features:**

- Support for resources, prompts, tools, and sampling
- Agentic mode with ReAct and orchestrator capabilities
- Seamless integration with OpenAI models and other LLMs
- Dynamic tool and resource management across multiple servers
- Support for both stdio and SSE transport protocols
- Comprehensive tool orchestration and resource analysis capabilities

### Memex

[Memex](https://memex.tech/) is the first MCP client and MCP server builder - all-in-one desktop app. Unlike traditional MCP clients that only consume existing servers, Memex can create custom MCP servers from natural language prompts, immediately integrate them into its toolkit, and use them to solve problems—all within a single conversation.

**Key features:**

- **Prompt-to-MCP Server**: Generate fully functional MCP servers from natural language descriptions
- **Self-Testing & Debugging**: Autonomously test, debug, and improve created MCP servers
- **Universal MCP Client**: Works with any MCP server through intuitive, natural language integration
- **Curated MCP Directory**: Access to tested, one-click installable MCP servers (Neon, Netlify, GitHub, Context7, and more)
- **Multi-Server Orchestration**: Leverage multiple MCP servers simultaneously for complex workflows

**Learn more:**

- [Memex Launch 2: MCP Teams and Agent API](https://memex.tech/blog/memex-launch-2-mcp-teams-and-agent-api-private-preview-125f)

### Microsoft Copilot Studio

[Microsoft Copilot Studio] is a robust SaaS platform designed for building custom AI-driven applications and intelligent agents, empowering developers to create, deploy, and manage sophisticated AI solutions.

**Key features:**

- Support for MCP tools
- Extend Copilot Studio agents with MCP servers
- Leveraging Microsoft unified, governed, and secure API management solutions

### MindPal

[MindPal](https://mindpal.io) is a no-code platform for building and running AI agents and multi-agent workflows for business processes.

**Key features:**

- Build custom AI agents with no-code
- Connect any SSE MCP server to extend agent tools
- Create multi-agent workflows for complex business processes
- User-friendly for both technical and non-technical professionals
- Ongoing development with continuous improvement of MCP support

**Learn more:**

- [MindPal MCP Documentation](https://docs.mindpal.io/agent/mcp)

### MooPoint

[MooPoint](https://moopoint.io)

MooPoint is a web-based AI chat platform built for developers and advanced users, letting you interact with multiple large language models (LLMs) through a single, unified interface. Connect your own API keys (OpenAI, Anthropic, and more) and securely manage custom MCP server integrations.

**Key features:**

- Accessible from any PC or smartphone—no installation required
- Choose your preferred LLM provider
- Supports `SSE`, `Streamable HTTP`, `npx`, and `uvx` MCP servers
- OAuth and sampling support
- New features added daily

### Msty Studio

[Msty Studio](https://msty.ai) is a privacy-first AI productivity platform that seamlessly integrates local and online language models (LLMs) into customizable workflows. Designed for both technical and non-technical users, Msty Studio offers a suite of tools to enhance AI interactions, automate tasks, and maintain full control over data and model behavior.

**Key features:**

- **Toolbox & Toolsets**: Connect AI models to local tools and scripts using MCP-compliant configurations. Group tools into Toolsets to enable dynamic, multi-step workflows within conversations.
- **Turnstiles**: Create automated, multi-step AI interactions, allowing for complex data processing and decision-making flows.
- **Real-Time Data Integration**: Enhance AI responses with up-to-date information by integrating real-time web search capabilities.
- **Split Chats & Branching**: Engage in parallel conversations with multiple models simultaneously, enabling comparative analysis and diverse perspectives.

**Learn more:**

- [Msty Studio Documentation](https://docs.msty.studio/features/toolbox/tools)

### NVIDIA Agent Intelligence (AIQ) toolkit

[NVIDIA Agent Intelligence (AIQ) toolkit](https://github.com/NVIDIA/AIQToolkit) is a flexible, lightweight, and unifying library that allows you to easily connect existing enterprise agents to data sources and tools across any framework.

**Key features:**

- Acts as an MCP **client** to consume remote tools
- Acts as an MCP **server** to expose tools
- Framework agnostic and compatible with LangChain, CrewAI, Semantic Kernel, and custom agents
- Includes built-in observability and evaluation tools

**Learn more:**

- [AIQ toolkit GitHub repository](https://github.com/NVIDIA/AIQToolkit)
- [AIQ toolkit MCP documentation](https://docs.nvidia.com/aiqtoolkit/latest/workflows/mcp/index.html)

### OpenSumi

[OpenSumi](https://github.com/opensumi/core) is a framework helps you quickly build AI Native IDE products.

**Key features:**

- Supports MCP tools in OpenSumi
- Supports built-in IDE MCP servers and custom MCP servers

### oterm

[oterm] is a terminal client for Ollama allowing users to create chats/agents.

**Key features:**

- Support for multiple fully customizable chat sessions with Ollama connected with tools.
- Support for MCP tools.

### Roo Code

[Roo Code](https://roocode.com) enables AI coding assistance via MCP.

**Key features:**

- Support for MCP tools and resources
- Integration with development workflows
- Extensible AI capabilities

### Postman

[Postman](https://postman.com/downloads) is the most popular API client and now supports MCP server testing and debugging.

**Key features:**

- Full support of all major MCP features (tools, prompts, resources, and subscriptions)
- Fast, seamless UI for debugging MCP capabilities
- MCP config integration (Claude, VSCode, etc.) for fast first-time experience in testing MCPs
- Integration with history, variables, and collections for reuse and collaboration

### Slack MCP Client

[Slack MCP Client](https://github.com/tuannvm/slack-mcp-client) acts as a bridge between Slack and Model Context Protocol (MCP) servers. Using Slack as the interface, it enables large language models (LLMs) to connect and interact with various MCP servers through standardized MCP tools.

**Key features:**

- **Supports Popular LLM Providers:** Integrates seamlessly with leading large language model providers such as OpenAI, Anthropic, and Ollama, allowing users to leverage advanced conversational AI and orchestration capabilities within Slack.
- **Dynamic and Secure Integration:** Supports dynamic registration of MCP tools, works in both channels and direct messages and manages credentials securely via environment variables or Kubernetes secrets.
- **Easy Deployment and Extensibility:** Offers official Docker images, a Helm chart for Kubernetes, and Docker Compose for local development, making it simple to deploy, configure, and extend with additional MCP servers or tools.

### Sourcegraph Cody

[Cody](https://openctx.org/docs/providers/modelcontextprotocol) is Sourcegraph's AI coding assistant, which implements MCP through OpenCTX.

**Key features:**

- Support for MCP resources
- Integration with Sourcegraph's code intelligence
- Uses OpenCTX as an abstraction layer
- Future support planned for additional MCP features

### SpinAI

[SpinAI](https://spinai.dev) is an open-source TypeScript framework for building observable AI agents. The framework provides native MCP compatibility, allowing agents to seamlessly integrate with MCP servers and tools.

**Key features:**

- Built-in MCP compatibility for AI agents
- Open-source TypeScript framework
- Observable agent architecture
- Native support for MCP tools integration

### Superinterface

[Superinterface](https://superinterface.ai) is AI infrastructure and a developer platform to build in-app AI assistants with support for MCP, interactive components, client-side function calling and more.

**Key features:**

- Use tools from MCP servers in assistants embedded via React components or script tags
- SSE transport support
- Use any AI model from any AI provider (OpenAI, Anthropic, Ollama, others)

### Superjoin

[Superjoin](https://superjoin.ai) brings the power of MCP directly into Google Sheets extension. With Superjoin, users can access and invoke MCP tools and agents without leaving their spreadsheets, enabling powerful AI workflows and automation right where their data lives.

**Key features:**

- Native Google Sheets add-on providing effortless access to MCP capabilities
- Supports OAuth 2.1 and header-based authentication for secure and flexible connections
- Compatible with both SSE and Streamable HTTP transport for efficient, real-time streaming communication
- Fully web-based, cross-platform client requiring no additional software installation

### TheiaAI/TheiaIDE

[Theia AI](https://eclipsesource.com/blogs/2024/10/07/introducing-theia-ai/) is a framework for building AI-enhanced tools and IDEs. The [AI-powered Theia IDE](https://eclipsesource.com/blogs/2024/10/08/introducting-ai-theia-ide/) is an open and flexible development environment built on Theia AI.

**Key features:**

- **Tool Integration**: Theia AI enables AI agents, including those in the Theia IDE, to utilize MCP servers for seamless tool interaction.
- **Customizable Prompts**: The Theia IDE allows users to define and adapt prompts, dynamically integrating MCP servers for tailored workflows.
- **Custom agents**: The Theia IDE supports creating custom agents that leverage MCP capabilities, enabling users to design dedicated workflows on the fly.

Theia AI and Theia IDE's MCP integration provide users with flexibility, making them powerful platforms for exploring and adapting MCP.

**Learn more:**

- [Theia IDE and Theia AI MCP Announcement](https://eclipsesource.com/blogs/2024/12/19/theia-ide-and-theia-ai-support-mcp/)
- [Download the AI-powered Theia IDE](https://theia-ide.org/)

### Tome

[Tome](https://github.com/runebookai/tome) is an open source cross-platform desktop app designed for working with local LLMs and MCP servers. It is designed to be beginner friendly and abstract away the nitty gritty of configuration for people getting started with MCP.

**Key features:**

- MCP servers are managed by Tome so there is no need to install uv or npm or configure JSON
- Users can quickly add or remove MCP servers via UI
- Any tool-supported local model on Ollama is compatible

### TypingMind App

[TypingMind](https://www.typingmind.com) is an advanced frontend for LLMs with MCP support. TypingMind supports all popular LLM providers like OpenAI, Gemini, Claude, and users can use with their own API keys.

**Key features:**

- **MCP Tool Integration**: Once MCP is configured, MCP tools will show up as plugins that can be enabled/disabled easily via the main app interface.
- **Assign MCP Tools to Agents**: TypingMind allows users to create AI agents that have a set of MCP servers assigned.
- **Remote MCP servers**: Allows users to customize where to run the MCP servers via its MCP Connector configuration, allowing the use of MCP tools across multiple devices (laptop, mobile devices, etc.) or control MCP servers from a remote private server.

**Learn more:**

- [TypingMind MCP Document](https://www.typingmind.com/mcp)
- [Download TypingMind (PWA)](https://www.typingmind.com/)

### VS Code GitHub Copilot

[VS Code](https://code.visualstudio.com/) integrates MCP with GitHub Copilot through [agent mode](https://code.visualstudio.com/docs/copilot/chat/chat-agent-mode), allowing direct interaction with MCP-provided tools within your agentic coding workflow. Configure servers in Claude Desktop, workspace or user settings, with guided MCP installation and secure handling of keys in input variables to avoid leaking hard-coded keys.

**Key features:**

- Support for stdio and server-sent events (SSE) transport
- Per-session selection of tools per agent session for optimal performance
- Easy server debugging with restart commands and output logging
- Tool calls with editable inputs and always-allow toggle
- Integration with existing VS Code extension system to register MCP servers from extensions

### Warp

[Warp](https://www.warp.dev/) is the intelligent terminal with AI and your dev team's knowledge built-in. With natural language capabilities integrated directly into an agentic command line, Warp enables developers to code, automate, and collaborate more efficiently -- all within a terminal that features a modern UX.

**Key features:**

- **Agent Mode with MCP support**: invoke tools and access data from MCP servers using natural language prompts
- **Flexible server management**: add and manage CLI or SSE-based MCP servers via Warp's built-in UI
- **Live tool/resource discovery**: view tools and resources from each running MCP server
- **Configurable startup**: set MCP servers to start automatically with Warp or launch them manually as needed

### WhatsMCP

[WhatsMCP](https://wassist.app/mcp/) is an MCP client for WhatsApp. WhatsMCP lets you interact with your AI stack from the comfort of a WhatsApp chat.

**Key features:**

- Supports MCP tools
- SSE transport, full OAuth2 support
- Chat flow management for WhatsApp messages
- One click setup for connecting to your MCP servers
- In chat management of MCP servers
- Oauth flow natively supported in WhatsApp

### Windsurf Editor

[Windsurf Editor](https://codeium.com/windsurf) is an agentic IDE that combines AI assistance with developer workflows. It features an innovative AI Flow system that enables both collaborative and independent AI interactions while maintaining developer control.

**Key features:**

- Revolutionary AI Flow paradigm for human-AI collaboration
- Intelligent code generation and understanding
- Rich development tools with multi-model support

### Witsy

[Witsy](https://github.com/nbonamy/witsy) is an AI desktop assistant, supporting Anthropic models and MCP servers as LLM tools.

**Key features:**

- Multiple MCP servers support
- Tool integration for executing commands and scripts
- Local server connections for enhanced privacy and security
- Easy-install from Smithery.ai
- Open-source, available for macOS, Windows and Linux

### Zed

[Zed](https://zed.dev/docs/assistant/model-context-protocol) is a high-performance code editor with built-in MCP support, focusing on prompt templates and tool integration.

**Key features:**

- Prompt templates surface as slash commands in the editor
- Tool integration for enhanced coding workflows
- Tight integration with editor features and workspace context
- Does not support MCP resources

### Zencoder

[Zencoder](https://zecoder.ai) is a coding agent that's available as an extension for VS Code and JetBrains family of IDEs, meeting developers where they already work. It comes with RepoGrokking (deep contextual codebase understanding), agentic pipeline, and the ability to create and share custom agents.

**Key features:**

- RepoGrokking - deep contextual understanding of codebases
- Agentic pipeline - runs, tests, and executes code before outputting it
- Zen Agents platform - ability to build and create custom agents and share with the team
- Integrated MCP tool library with one-click installations
- Specialized agents for Unit and E2E Testing

**Learn more:**

- [Zencoder Documentation](https://docs.zencoder.ai)

## Adding MCP support to your application

If you've added MCP support to your application, we encourage you to submit a pull request to add it to this list. MCP integration can provide your users with powerful contextual AI capabilities and make your application part of the growing MCP ecosystem.

Benefits of adding MCP support:

- Enable users to bring their own context and tools
- Join a growing ecosystem of interoperable AI applications
- Provide users with flexible integration options
- Support local-first AI workflows

To get started with implementing MCP in your application, check out our [Python](https://github.com/modelcontextprotocol/python-sdk) or [TypeScript SDK Documentation](https://github.com/modelcontextprotocol/typescript-sdk)

## Updates and corrections

This list is maintained by the community. If you notice any inaccuracies or would like to update information about MCP support in your application, please submit a pull request or [open an issue in our documentation repository](https://github.com/modelcontextprotocol/modelcontextprotocol/issues).
