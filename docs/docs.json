{"$schema": "https://mintlify.com/docs.json", "theme": "mint", "name": "Model Context Protocol", "colors": {"primary": "#09090b", "light": "#FAFAFA", "dark": "#09090b"}, "favicon": "/favicon.svg", "navbar": {"links": [{"label": "GitHub", "href": "https://github.com/modelcontextprotocol"}]}, "navigation": {"versions": [{"version": "Version 2025-06-18 (latest)", "groups": [{"group": "User Guide", "pages": ["introduction", {"group": "Quickstart", "pages": ["quickstart/server", "quickstart/client", "quickstart/user"]}, {"group": "Concepts", "pages": ["docs/concepts/architecture", "docs/concepts/resources", "docs/concepts/prompts", "docs/concepts/tools", "docs/concepts/sampling", "docs/concepts/roots", "docs/concepts/transports"]}, {"group": "Examples", "pages": ["examples", "clients"]}, {"group": "Tutorials", "pages": ["tutorials/building-mcp-with-llms", "docs/tools/debugging", "docs/tools/inspector"]}, "faqs"]}, {"group": "Protocol", "pages": ["specification/2025-06-18/index", "specification/2025-06-18/changelog", "specification/2025-06-18/architecture/index", {"group": "Base Protocol", "pages": ["specification/2025-06-18/basic/index", "specification/2025-06-18/basic/lifecycle", "specification/2025-06-18/basic/transports", "specification/2025-06-18/basic/authorization", "specification/2025-06-18/basic/security_best_practices", {"group": "Utilities", "pages": ["specification/2025-06-18/basic/utilities/cancellation", "specification/2025-06-18/basic/utilities/ping", "specification/2025-06-18/basic/utilities/progress"]}]}, {"group": "Client Features", "pages": ["specification/2025-06-18/client/roots", "specification/2025-06-18/client/sampling", "specification/2025-06-18/client/elicitation"]}, {"group": "Server Features", "pages": ["specification/2025-06-18/server/index", "specification/2025-06-18/server/prompts", "specification/2025-06-18/server/resources", "specification/2025-06-18/server/tools", {"group": "Utilities", "pages": ["specification/2025-06-18/server/utilities/completion", "specification/2025-06-18/server/utilities/logging", "specification/2025-06-18/server/utilities/pagination"]}]}]}, {"group": "Development", "pages": ["specification/versioning", "development/roadmap", "development/contributing"]}, {"group": "SDKs", "pages": ["links/sdks/csharp", "links/sdks/java", "links/sdks/kotlin", "links/sdks/python", "links/sdks/ruby", "links/sdks/swift", "links/sdks/typescript"]}]}, {"version": "Version 2025-03-26", "groups": [{"group": "User Guide", "pages": ["introduction", {"group": "Quickstart", "pages": ["quickstart/server", "quickstart/client", "quickstart/user"]}, {"group": "Concepts", "pages": ["docs/concepts/architecture", "docs/concepts/resources", "docs/concepts/prompts", "docs/concepts/tools", "docs/concepts/sampling", "docs/concepts/roots", "docs/concepts/transports"]}, {"group": "Examples", "pages": ["examples", "clients"]}, {"group": "Tutorials", "pages": ["tutorials/building-mcp-with-llms", "docs/tools/debugging", "docs/tools/inspector"]}, "faqs"]}, {"group": "Protocol", "pages": ["specification/2025-03-26/index", "specification/2025-03-26/changelog", "specification/2025-03-26/architecture/index", {"group": "Base Protocol", "pages": ["specification/2025-03-26/basic/index", "specification/2025-03-26/basic/lifecycle", "specification/2025-03-26/basic/transports", "specification/2025-03-26/basic/authorization", {"group": "Utilities", "pages": ["specification/2025-03-26/basic/utilities/cancellation", "specification/2025-03-26/basic/utilities/ping", "specification/2025-03-26/basic/utilities/progress"]}]}, {"group": "Client Features", "pages": ["specification/2025-03-26/client/roots", "specification/2025-03-26/client/sampling"]}, {"group": "Server Features", "pages": ["specification/2025-03-26/server/index", "specification/2025-03-26/server/prompts", "specification/2025-03-26/server/resources", "specification/2025-03-26/server/tools", {"group": "Utilities", "pages": ["specification/2025-03-26/server/utilities/completion", "specification/2025-03-26/server/utilities/logging", "specification/2025-03-26/server/utilities/pagination"]}]}]}, {"group": "Development", "pages": ["specification/versioning", "development/roadmap", "development/contributing"]}, {"group": "SDKs", "pages": ["links/sdks/csharp", "links/sdks/java", "links/sdks/kotlin", "links/sdks/python", "links/sdks/ruby", "links/sdks/swift", "links/sdks/typescript"]}]}, {"version": "Version 2024-11-05", "groups": [{"group": "User Guide", "pages": ["introduction", {"group": "Quickstart", "pages": ["quickstart/server", "quickstart/client", "quickstart/user"]}, {"group": "Concepts", "pages": ["docs/concepts/architecture", "docs/concepts/resources", "docs/concepts/prompts", "docs/concepts/tools", "docs/concepts/sampling", "docs/concepts/roots", "docs/concepts/transports"]}, {"group": "Examples", "pages": ["examples", "clients"]}, {"group": "Tutorials", "pages": ["tutorials/building-mcp-with-llms", "docs/tools/debugging", "docs/tools/inspector"]}, "faqs"]}, {"group": "Protocol", "pages": ["specification/2024-11-05/index", "specification/2024-11-05/architecture/index", {"group": "Base Protocol", "pages": ["specification/2024-11-05/basic/index", "specification/2024-11-05/basic/lifecycle", "specification/2024-11-05/basic/messages", "specification/2024-11-05/basic/transports", {"group": "Utilities", "pages": ["specification/2024-11-05/basic/utilities/cancellation", "specification/2024-11-05/basic/utilities/ping", "specification/2024-11-05/basic/utilities/progress"]}]}, {"group": "Client Features", "pages": ["specification/2024-11-05/client/roots", "specification/2024-11-05/client/sampling"]}, {"group": "Server Features", "pages": ["specification/2024-11-05/server/index", "specification/2024-11-05/server/prompts", "specification/2024-11-05/server/resources", "specification/2024-11-05/server/tools", {"group": "Utilities", "pages": ["specification/2024-11-05/server/utilities/completion", "specification/2024-11-05/server/utilities/logging", "specification/2024-11-05/server/utilities/pagination"]}]}]}, {"group": "Development", "pages": ["specification/versioning", "development/roadmap", "development/contributing"]}, {"group": "SDKs", "pages": ["links/sdks/csharp", "links/sdks/java", "links/sdks/kotlin", "links/sdks/python", "links/sdks/ruby", "links/sdks/swift", "links/sdks/typescript"]}]}, {"version": "Draft", "groups": [{"group": "User Guide", "pages": ["introduction", {"group": "Quickstart", "pages": ["quickstart/server", "quickstart/client", "quickstart/user"]}, {"group": "Concepts", "pages": ["docs/concepts/architecture", "docs/concepts/resources", "docs/concepts/prompts", "docs/concepts/tools", "docs/concepts/sampling", "docs/concepts/roots", "docs/concepts/transports"]}, {"group": "Examples", "pages": ["examples", "clients"]}, {"group": "Tutorials", "pages": ["tutorials/building-mcp-with-llms", "docs/tools/debugging", "docs/tools/inspector"]}, "faqs"]}, {"group": "Protocol", "pages": ["specification/draft/index", "specification/draft/changelog", "specification/draft/architecture/index", {"group": "Base Protocol", "pages": ["specification/draft/basic/index", "specification/draft/basic/lifecycle", "specification/draft/basic/transports", "specification/draft/basic/authorization", "specification/draft/basic/security_best_practices", {"group": "Utilities", "pages": ["specification/draft/basic/utilities/cancellation", "specification/draft/basic/utilities/ping", "specification/draft/basic/utilities/progress"]}]}, {"group": "Client Features", "pages": ["specification/draft/client/roots", "specification/draft/client/sampling", "specification/draft/client/elicitation"]}, {"group": "Server Features", "pages": ["specification/draft/server/index", "specification/draft/server/prompts", "specification/draft/server/resources", "specification/draft/server/tools", {"group": "Utilities", "pages": ["specification/draft/server/utilities/completion", "specification/draft/server/utilities/logging", "specification/draft/server/utilities/pagination"]}]}]}, {"group": "Development", "pages": ["specification/versioning", "development/roadmap", "development/contributing"]}, {"group": "SDKs", "pages": ["links/sdks/csharp", "links/sdks/java", "links/sdks/kotlin", "links/sdks/python", "links/sdks/ruby", "links/sdks/swift", "links/sdks/typescript"]}]}]}, "logo": {"light": "/logo/light.svg", "dark": "/logo/dark.svg"}, "seo": {"metatags": {"og:image": "https://raw.githubusercontent.com/modelcontextprotocol/docs/2eb6171ddbfeefde349dc3b8d5e2b87414c26250/images/og-image.png"}, "indexing": "navigable"}, "footer": {"socials": {"github": "https://github.com/modelcontextprotocol"}}, "redirects": [{"source": "/tutorials/building-a-client", "destination": "/quickstart/client"}, {"source": "/quickstart", "destination": "/quickstart/server"}, {"source": "/specification/latest", "destination": "/specification/2025-06-18", "permanent": false}], "contextual": {"options": ["copy", "view"]}}