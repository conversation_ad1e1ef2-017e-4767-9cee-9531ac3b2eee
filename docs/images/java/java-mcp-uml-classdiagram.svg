<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="1616px" preserveAspectRatio="none" style="width:2294px;height:1616px;background:#FFFFFF;" version="1.1" viewBox="0 0 2294 1616" width="2294px" zoomAndPan="magnify"><defs/><g><!--class McpTransport--><g id="elem_McpTransport"><rect codeLine="2" fill="#F1F1F1" height="129.4844" id="McpTransport" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="710.9541" x="731.5" y="1091"/><ellipse cx="1034.52" cy="1107" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1030.4419,1102.7656 L1030.4419,1100.6094 L1037.8325,1100.6094 L1037.8325,1102.7656 L1035.3638,1102.7656 L1035.3638,1110.8438 L1037.8325,1110.8438 L1037.8325,1113 L1030.4419,1113 L1030.4419,1110.8438 L1032.9106,1110.8438 L1032.9106,1102.7656 L1030.4419,1102.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="96.4141" x="1055.02" y="1111.8467">McpTransport</text><line style="stroke:#181818;stroke-width:0.5;" x1="732.5" x2="1441.4541" y1="1123" y2="1123"/><line style="stroke:#181818;stroke-width:0.5;" x1="732.5" x2="1441.4541" y1="1131" y2="1131"/><ellipse cx="742.5" cy="1144.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="684.9541" x="751.5" y="1147.9951">Mono&lt;Void&gt; connect(Function&lt;Mono&lt;JSONRPCMessage&gt;, Mono&lt;JSONRPCMessage&gt;&gt; handler)</text><ellipse cx="742.5" cy="1160.9453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="394.0439" x="751.5" y="1164.292">Mono&lt;Void&gt; sendMessage(JSONRPCMessage message)</text><ellipse cx="742.5" cy="1177.2422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="81.0605" x="751.5" y="1180.5889">void close()</text><ellipse cx="742.5" cy="1193.5391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="215.2568" x="751.5" y="1196.8857">Mono&lt;Void&gt; closeGracefully()</text><ellipse cx="742.5" cy="1209.8359" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="458.0146" x="751.5" y="1213.1826">&lt;T&gt; T unmarshalFrom(Object data, TypeReference&lt;T&gt; typeRef)</text></g><!--class McpSession--><g id="elem_McpSession"><rect codeLine="10" fill="#F1F1F1" height="113.1875" id="McpSession" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="709.833" x="732" y="246"/><ellipse cx="1041.627" cy="262" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1037.5488,257.7656 L1037.5488,255.6094 L1044.9395,255.6094 L1044.9395,257.7656 L1042.4707,257.7656 L1042.4707,265.8438 L1044.9395,265.8438 L1044.9395,268 L1037.5488,268 L1037.5488,265.8438 L1040.0176,265.8438 L1040.0176,257.7656 L1037.5488,257.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="82.0791" x="1062.127" y="266.8467">McpSession</text><line style="stroke:#181818;stroke-width:0.5;" x1="733" x2="1440.833" y1="278" y2="278"/><line style="stroke:#181818;stroke-width:0.5;" x1="733" x2="1440.833" y1="286" y2="286"/><ellipse cx="743" cy="299.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="683.833" x="752" y="302.9951">&lt;T&gt; Mono&lt;T&gt; sendRequest(String method, Object requestParams, TypeReference&lt;T&gt; typeRef)</text><ellipse cx="743" cy="315.9453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="537.4961" x="752" y="319.292">Mono&lt;Void&gt; sendNotification(String method, Map&lt;String, Object&gt; params)</text><ellipse cx="743" cy="332.2422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="215.2568" x="752" y="335.5889">Mono&lt;Void&gt; closeGracefully()</text><ellipse cx="743" cy="348.5391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="81.0605" x="752" y="351.8857">void close()</text></g><!--class DefaultMcpSession--><g id="elem_DefaultMcpSession"><rect codeLine="18" fill="#F1F1F1" height="80.5938" id="DefaultMcpSession" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="228.3438" x="973" y="615"/><ellipse cx="1016.4001" cy="631" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1019.3688,636.6406 Q1018.7907,636.9375 1018.1501,637.0781 Q1017.5095,637.2344 1016.8063,637.2344 Q1014.3063,637.2344 1012.9782,635.5938 Q1011.6657,633.9375 1011.6657,630.8125 Q1011.6657,627.6875 1012.9782,626.0313 Q1014.3063,624.375 1016.8063,624.375 Q1017.5095,624.375 1018.1501,624.5313 Q1018.8063,624.6875 1019.3688,624.9844 L1019.3688,627.7031 Q1018.7438,627.125 1018.1501,626.8594 Q1017.5563,626.5781 1016.9313,626.5781 Q1015.5876,626.5781 1014.9001,627.6563 Q1014.2126,628.7188 1014.2126,630.8125 Q1014.2126,632.9063 1014.9001,633.9844 Q1015.5876,635.0469 1016.9313,635.0469 Q1017.5563,635.0469 1018.1501,634.7813 Q1018.7438,634.5 1019.3688,633.9219 L1019.3688,636.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="133.2324" x="1036.7112" y="635.8467">DefaultMcpSession</text><line style="stroke:#181818;stroke-width:0.5;" x1="974" x2="1200.3438" y1="647" y2="647"/><ellipse cx="984" cy="660.6484" fill="none" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="179.5117" x="993" y="663.9951">interface RequestHandler</text><ellipse cx="984" cy="676.9453" fill="none" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="202.3438" x="993" y="680.292">interface NotificationHandler</text><line style="stroke:#181818;stroke-width:0.5;" x1="974" x2="1200.3438" y1="687.5938" y2="687.5938"/></g><!--class McpClient--><g id="elem_McpClient"><rect codeLine="24" fill="#F1F1F1" height="64.2969" id="McpClient" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="334.7861" x="316.5" y="270.5"/><ellipse cx="445.0464" cy="286.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M448.0151,292.1406 Q447.437,292.4375 446.7964,292.5781 Q446.1558,292.7344 445.4526,292.7344 Q442.9526,292.7344 441.6245,291.0938 Q440.312,289.4375 440.312,286.3125 Q440.312,283.1875 441.6245,281.5313 Q442.9526,279.875 445.4526,279.875 Q446.1558,279.875 446.7964,280.0313 Q447.4526,280.1875 448.0151,280.4844 L448.0151,283.2031 Q447.3901,282.625 446.7964,282.3594 Q446.2026,282.0781 445.5776,282.0781 Q444.2339,282.0781 443.5464,283.1563 Q442.8589,284.2188 442.8589,286.3125 Q442.8589,288.4063 443.5464,289.4844 Q444.2339,290.5469 445.5776,290.5469 Q446.2026,290.5469 446.7964,290.2813 Q447.3901,290 448.0151,289.4219 L448.0151,292.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="69.1934" x="465.5464" y="291.3467">McpClient</text><line style="stroke:#181818;stroke-width:0.5;" x1="317.5" x2="650.2861" y1="302.5" y2="302.5"/><line style="stroke:#181818;stroke-width:0.5;" x1="317.5" x2="650.2861" y1="310.5" y2="310.5"/><ellipse cx="327.5" cy="324.1484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" text-decoration="underline" textLength="308.7861" x="336.5" y="327.4951">Builder using(ClientMcpTransport transport)</text></g><!--class McpAsyncClient--><g id="elem_McpAsyncClient"><rect codeLine="28" fill="#F1F1F1" height="390.2344" id="McpAsyncClient" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="561.1445" x="135.5" y="960.5"/><ellipse cx="356.3623" cy="976.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M359.3311,982.1406 Q358.7529,982.4375 358.1123,982.5781 Q357.4717,982.7344 356.7686,982.7344 Q354.2686,982.7344 352.9404,981.0938 Q351.6279,979.4375 351.6279,976.3125 Q351.6279,973.1875 352.9404,971.5313 Q354.2686,969.875 356.7686,969.875 Q357.4717,969.875 358.1123,970.0313 Q358.7686,970.1875 359.3311,970.4844 L359.3311,973.2031 Q358.7061,972.625 358.1123,972.3594 Q357.5186,972.0781 356.8936,972.0781 Q355.5498,972.0781 354.8623,973.1563 Q354.1748,974.2188 354.1748,976.3125 Q354.1748,978.4063 354.8623,979.4844 Q355.5498,980.5469 356.8936,980.5469 Q357.5186,980.5469 358.1123,980.2813 Q358.7061,980 359.3311,979.4219 L359.3311,982.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="110.9199" x="376.8623" y="981.3467">McpAsyncClient</text><line style="stroke:#181818;stroke-width:0.5;" x1="136.5" x2="695.6445" y1="992.5" y2="992.5"/><line style="stroke:#181818;stroke-width:0.5;" x1="136.5" x2="695.6445" y1="1000.5" y2="1000.5"/><ellipse cx="146.5" cy="1014.1484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="237.7471" x="155.5" y="1017.4951">Mono&lt;InitializeResult&gt; initialize()</text><ellipse cx="146.5" cy="1030.4453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="293.5078" x="155.5" y="1033.792">ServerCapabilities getServerCapabilities()</text><ellipse cx="146.5" cy="1046.7422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="221.9355" x="155.5" y="1050.0889">Implementation getServerInfo()</text><ellipse cx="146.5" cy="1063.0391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="282.748" x="155.5" y="1066.3857">ClientCapabilities getClientCapabilities()</text><ellipse cx="146.5" cy="1079.3359" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="216.5557" x="155.5" y="1082.6826">Implementation getClientInfo()</text><ellipse cx="146.5" cy="1095.6328" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="81.0605" x="155.5" y="1098.9795">void close()</text><ellipse cx="146.5" cy="1111.9297" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="215.2568" x="155.5" y="1115.2764">Mono&lt;Void&gt; closeGracefully()</text><ellipse cx="146.5" cy="1128.2266" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="153.0498" x="155.5" y="1131.5732">Mono&lt;Object&gt; ping()</text><ellipse cx="146.5" cy="1144.5234" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="231.7109" x="155.5" y="1147.8701">Mono&lt;Void&gt; addRoot(Root root)</text><ellipse cx="146.5" cy="1160.8203" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="288.1553" x="155.5" y="1164.167">Mono&lt;Void&gt; removeRoot(String rootUri)</text><ellipse cx="146.5" cy="1177.1172" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="310.7959" x="155.5" y="1180.4639">Mono&lt;Void&gt; rootsListChangedNotification()</text><ellipse cx="146.5" cy="1193.4141" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="401.3174" x="155.5" y="1196.7607">Mono&lt;CallToolResult&gt; callTool(CallToolRequest request)</text><ellipse cx="146.5" cy="1209.7109" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="239.5723" x="155.5" y="1213.0576">Mono&lt;ListToolsResult&gt; listTools()</text><ellipse cx="146.5" cy="1226.0078" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="310.707" x="155.5" y="1229.3545">Mono&lt;ListResourcesResult&gt; listResources()</text><ellipse cx="146.5" cy="1242.3047" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="535.1445" x="155.5" y="1245.6514">Mono&lt;ReadResourceResult&gt; readResource(ReadResourceRequest request)</text><ellipse cx="146.5" cy="1258.6016" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="443.2285" x="155.5" y="1261.9482">Mono&lt;ListResourceTemplatesResult&gt; listResourceTemplates()</text><ellipse cx="146.5" cy="1274.8984" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="424.4229" x="155.5" y="1278.2451">Mono&lt;Void&gt; subscribeResource(SubscribeRequest request)</text><ellipse cx="146.5" cy="1291.1953" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="459.6963" x="155.5" y="1294.542">Mono&lt;Void&gt; unsubscribeResource(UnsubscribeRequest request)</text><ellipse cx="146.5" cy="1307.4922" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="281.9824" x="155.5" y="1310.8389">Mono&lt;ListPromptsResult&gt; listPrompts()</text><ellipse cx="146.5" cy="1323.7891" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="461.501" x="155.5" y="1327.1357">Mono&lt;GetPromptResult&gt; getPrompt(GetPromptRequest request)</text><ellipse cx="146.5" cy="1340.0859" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="352.9531" x="155.5" y="1343.4326">Mono&lt;Void&gt; setLoggingLevel(LoggingLevel level)</text></g><!--class McpSyncClient--><g id="elem_McpSyncClient"><rect codeLine="52" fill="#F1F1F1" height="390.2344" id="McpSyncClient" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="499.6006" x="7" y="460.5"/><ellipse cx="201.0825" cy="476.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M204.0513,482.1406 Q203.4731,482.4375 202.8325,482.5781 Q202.1919,482.7344 201.4888,482.7344 Q198.9888,482.7344 197.6606,481.0938 Q196.3481,479.4375 196.3481,476.3125 Q196.3481,473.1875 197.6606,471.5313 Q198.9888,469.875 201.4888,469.875 Q202.1919,469.875 202.8325,470.0313 Q203.4888,470.1875 204.0513,470.4844 L204.0513,473.2031 Q203.4263,472.625 202.8325,472.3594 Q202.2388,472.0781 201.6138,472.0781 Q200.27,472.0781 199.5825,473.1563 Q198.895,474.2188 198.895,476.3125 Q198.895,478.4063 199.5825,479.4844 Q200.27,480.5469 201.6138,480.5469 Q202.2388,480.5469 202.8325,480.2813 Q203.4263,480 204.0513,479.4219 L204.0513,482.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="102.9355" x="221.5825" y="481.3467">McpSyncClient</text><line style="stroke:#181818;stroke-width:0.5;" x1="8" x2="505.6006" y1="492.5" y2="492.5"/><line style="stroke:#181818;stroke-width:0.5;" x1="8" x2="505.6006" y1="500.5" y2="500.5"/><ellipse cx="18" cy="514.1484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="176.2031" x="27" y="517.4951">InitializeResult initialize()</text><ellipse cx="18" cy="530.4453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="293.5078" x="27" y="533.792">ServerCapabilities getServerCapabilities()</text><ellipse cx="18" cy="546.7422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="221.9355" x="27" y="550.0889">Implementation getServerInfo()</text><ellipse cx="18" cy="563.0391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="282.748" x="27" y="566.3857">ClientCapabilities getClientCapabilities()</text><ellipse cx="18" cy="579.3359" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="216.5557" x="27" y="582.6826">Implementation getClientInfo()</text><ellipse cx="18" cy="595.6328" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="81.0605" x="27" y="598.9795">void close()</text><ellipse cx="18" cy="611.9297" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="178.7666" x="27" y="615.2764">boolean closeGracefully()</text><ellipse cx="18" cy="628.2266" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="91.5059" x="27" y="631.5732">Object ping()</text><ellipse cx="18" cy="644.5234" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="168.875" x="27" y="647.8701">void addRoot(Root root)</text><ellipse cx="18" cy="660.8203" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="225.3193" x="27" y="664.167">void removeRoot(String rootUri)</text><ellipse cx="18" cy="677.1172" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="247.96" x="27" y="680.4639">void rootsListChangedNotification()</text><ellipse cx="18" cy="693.4141" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="339.7734" x="27" y="696.7607">CallToolResult callTool(CallToolRequest request)</text><ellipse cx="18" cy="709.7109" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="178.0283" x="27" y="713.0576">ListToolsResult listTools()</text><ellipse cx="18" cy="726.0078" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="249.1631" x="27" y="729.3545">ListResourcesResult listResources()</text><ellipse cx="18" cy="742.3047" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="473.6006" x="27" y="745.6514">ReadResourceResult readResource(ReadResourceRequest request)</text><ellipse cx="18" cy="758.6016" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="381.6846" x="27" y="761.9482">ListResourceTemplatesResult listResourceTemplates()</text><ellipse cx="18" cy="774.8984" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="361.5869" x="27" y="778.2451">void subscribeResource(SubscribeRequest request)</text><ellipse cx="18" cy="791.1953" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="396.8604" x="27" y="794.542">void unsubscribeResource(UnsubscribeRequest request)</text><ellipse cx="18" cy="807.4922" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="220.4385" x="27" y="810.8389">ListPromptsResult listPrompts()</text><ellipse cx="18" cy="823.7891" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="399.957" x="27" y="827.1357">GetPromptResult getPrompt(GetPromptRequest request)</text><ellipse cx="18" cy="840.0859" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="290.1172" x="27" y="843.4326">void setLoggingLevel(LoggingLevel level)</text></g><!--class McpServer--><g id="elem_McpServer"><rect codeLine="77" fill="#F1F1F1" height="64.2969" id="McpServer" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="340.166" x="1639" y="270.5"/><ellipse cx="1767.5464" cy="286.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1770.5151,292.1406 Q1769.937,292.4375 1769.2964,292.5781 Q1768.6558,292.7344 1767.9526,292.7344 Q1765.4526,292.7344 1764.1245,291.0938 Q1762.812,289.4375 1762.812,286.3125 Q1762.812,283.1875 1764.1245,281.5313 Q1765.4526,279.875 1767.9526,279.875 Q1768.6558,279.875 1769.2964,280.0313 Q1769.9526,280.1875 1770.5151,280.4844 L1770.5151,283.2031 Q1769.8901,282.625 1769.2964,282.3594 Q1768.7026,282.0781 1768.0776,282.0781 Q1766.7339,282.0781 1766.0464,283.1563 Q1765.3589,284.2188 1765.3589,286.3125 Q1765.3589,288.4063 1766.0464,289.4844 Q1766.7339,290.5469 1768.0776,290.5469 Q1768.7026,290.5469 1769.2964,290.2813 Q1769.8901,290 1770.5151,289.4219 L1770.5151,292.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="74.5732" x="1788.0464" y="291.3467">McpServer</text><line style="stroke:#181818;stroke-width:0.5;" x1="1640" x2="1978.166" y1="302.5" y2="302.5"/><line style="stroke:#181818;stroke-width:0.5;" x1="1640" x2="1978.166" y1="310.5" y2="310.5"/><ellipse cx="1650" cy="324.1484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" text-decoration="underline" textLength="314.166" x="1659" y="327.4951">Builder using(ServerMcpTransport transport)</text></g><!--class McpAsyncServer--><g id="elem_McpAsyncServer"><rect codeLine="81" fill="#F1F1F1" height="406.5313" id="McpAsyncServer" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="584.7559" x="1477.5" y="952"/><ellipse cx="1707.478" cy="968" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1710.4468,973.6406 Q1709.8687,973.9375 1709.228,974.0781 Q1708.5874,974.2344 1707.8843,974.2344 Q1705.3843,974.2344 1704.0562,972.5938 Q1702.7437,970.9375 1702.7437,967.8125 Q1702.7437,964.6875 1704.0562,963.0313 Q1705.3843,961.375 1707.8843,961.375 Q1708.5874,961.375 1709.228,961.5313 Q1709.8843,961.6875 1710.4468,961.9844 L1710.4468,964.7031 Q1709.8218,964.125 1709.228,963.8594 Q1708.6343,963.5781 1708.0093,963.5781 Q1706.6655,963.5781 1705.978,964.6563 Q1705.2905,965.7188 1705.2905,967.8125 Q1705.2905,969.9063 1705.978,970.9844 Q1706.6655,972.0469 1708.0093,972.0469 Q1708.6343,972.0469 1709.228,971.7813 Q1709.8218,971.5 1710.4468,970.9219 L1710.4468,973.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="116.2998" x="1727.978" y="972.8467">McpAsyncServer</text><line style="stroke:#181818;stroke-width:0.5;" x1="1478.5" x2="2061.2559" y1="984" y2="984"/><line style="stroke:#181818;stroke-width:0.5;" x1="1478.5" x2="2061.2559" y1="992" y2="992"/><ellipse cx="1488.5" cy="1005.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="293.5078" x="1497.5" y="1008.9951">ServerCapabilities getServerCapabilities()</text><ellipse cx="1488.5" cy="1021.9453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="221.9355" x="1497.5" y="1025.292">Implementation getServerInfo()</text><ellipse cx="1488.5" cy="1038.2422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="282.748" x="1497.5" y="1041.5889">ClientCapabilities getClientCapabilities()</text><ellipse cx="1488.5" cy="1054.5391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="216.5557" x="1497.5" y="1057.8857">Implementation getClientInfo()</text><ellipse cx="1488.5" cy="1070.8359" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="81.0605" x="1497.5" y="1074.1826">void close()</text><ellipse cx="1488.5" cy="1087.1328" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="215.2568" x="1497.5" y="1090.4795">Mono&lt;Void&gt; closeGracefully()</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1497.5" y="1106.7764">&#160;</text><ellipse cx="1488.5" cy="1119.7266" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="394.3994" x="1497.5" y="1123.0732">Mono&lt;Void&gt; addTool(ToolRegistration toolRegistration)</text><ellipse cx="1488.5" cy="1136.0234" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="304.9238" x="1497.5" y="1139.3701">Mono&lt;Void&gt; removeTool(String toolName)</text><ellipse cx="1488.5" cy="1152.3203" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="271.6943" x="1497.5" y="1155.667">Mono&lt;Void&gt; notifyToolsListChanged()</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1497.5" y="1171.9639">&#160;</text><ellipse cx="1488.5" cy="1184.9141" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="470.2646" x="1497.5" y="1188.2607">Mono&lt;Void&gt; addResource(ResourceRegistration resourceHandler)</text><ellipse cx="1488.5" cy="1201.2109" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="353.7393" x="1497.5" y="1204.5576">Mono&lt;Void&gt; removeResource(String resourceUri)</text><ellipse cx="1488.5" cy="1217.5078" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="307.2617" x="1497.5" y="1220.8545">Mono&lt;Void&gt; notifyResourcesListChanged()</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1497.5" y="1237.1514">&#160;</text><ellipse cx="1488.5" cy="1250.1016" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="461.5215" x="1497.5" y="1253.4482">Mono&lt;Void&gt; addPrompt(PromptRegistration promptRegistration)</text><ellipse cx="1488.5" cy="1266.3984" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="350.8408" x="1497.5" y="1269.7451">Mono&lt;Void&gt; removePrompt(String promptName)</text><ellipse cx="1488.5" cy="1282.6953" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="292.8994" x="1497.5" y="1286.042">Mono&lt;Void&gt; notifyPromptsListChanged()</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1497.5" y="1302.3389">&#160;</text><ellipse cx="1488.5" cy="1315.2891" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="520.7002" x="1497.5" y="1318.6357">Mono&lt;Void&gt; loggingNotification(LoggingMessageNotification notification)</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1497.5" y="1334.9326">&#160;</text><ellipse cx="1488.5" cy="1347.8828" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="558.7559" x="1497.5" y="1351.2295">Mono&lt;CreateMessageResult&gt; createMessage(CreateMessageRequest request)</text></g><!--class McpSyncServer--><g id="elem_McpSyncServer"><rect codeLine="112" fill="#F1F1F1" height="439.125" id="McpSyncServer" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="523.2119" x="1764.5" y="436"/><ellipse cx="1967.6982" cy="452" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1970.667,457.6406 Q1970.0889,457.9375 1969.4482,458.0781 Q1968.8076,458.2344 1968.1045,458.2344 Q1965.6045,458.2344 1964.2764,456.5938 Q1962.9639,454.9375 1962.9639,451.8125 Q1962.9639,448.6875 1964.2764,447.0313 Q1965.6045,445.375 1968.1045,445.375 Q1968.8076,445.375 1969.4482,445.5313 Q1970.1045,445.6875 1970.667,445.9844 L1970.667,448.7031 Q1970.042,448.125 1969.4482,447.8594 Q1968.8545,447.5781 1968.2295,447.5781 Q1966.8857,447.5781 1966.1982,448.6563 Q1965.5107,449.7188 1965.5107,451.8125 Q1965.5107,453.9063 1966.1982,454.9844 Q1966.8857,456.0469 1968.2295,456.0469 Q1968.8545,456.0469 1969.4482,455.7813 Q1970.042,455.5 1970.667,454.9219 L1970.667,457.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="108.3154" x="1988.1982" y="456.8467">McpSyncServer</text><line style="stroke:#181818;stroke-width:0.5;" x1="1765.5" x2="2286.7119" y1="468" y2="468"/><line style="stroke:#181818;stroke-width:0.5;" x1="1765.5" x2="2286.7119" y1="476" y2="476"/><ellipse cx="1775.5" cy="489.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="242.2998" x="1784.5" y="492.9951">McpAsyncServer getAsyncServer()</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1784.5" y="509.292">&#160;</text><ellipse cx="1775.5" cy="522.2422" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="293.5078" x="1784.5" y="525.5889">ServerCapabilities getServerCapabilities()</text><ellipse cx="1775.5" cy="538.5391" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="221.9355" x="1784.5" y="541.8857">Implementation getServerInfo()</text><ellipse cx="1775.5" cy="554.8359" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="282.748" x="1784.5" y="558.1826">ClientCapabilities getClientCapabilities()</text><ellipse cx="1775.5" cy="571.1328" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="216.5557" x="1784.5" y="574.4795">Implementation getClientInfo()</text><ellipse cx="1775.5" cy="587.4297" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="81.0605" x="1784.5" y="590.7764">void close()</text><ellipse cx="1775.5" cy="603.7266" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="152.4209" x="1784.5" y="607.0732">void closeGracefully()</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1784.5" y="623.3701">&#160;</text><ellipse cx="1775.5" cy="636.3203" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="301.6357" x="1784.5" y="639.667">void addTool(ToolRegistration toolHandler)</text><ellipse cx="1775.5" cy="652.6172" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="242.0879" x="1784.5" y="655.9639">void removeTool(String toolName)</text><ellipse cx="1775.5" cy="668.9141" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="208.8584" x="1784.5" y="672.2607">void notifyToolsListChanged()</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1784.5" y="688.5576">&#160;</text><ellipse cx="1775.5" cy="701.5078" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="407.4287" x="1784.5" y="704.8545">void addResource(ResourceRegistration resourceHandler)</text><ellipse cx="1775.5" cy="717.8047" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="290.9033" x="1784.5" y="721.1514">void removeResource(String resourceUri)</text><ellipse cx="1775.5" cy="734.1016" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="244.4258" x="1784.5" y="737.4482">void notifyResourcesListChanged()</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1784.5" y="753.7451">&#160;</text><ellipse cx="1775.5" cy="766.6953" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="398.6855" x="1784.5" y="770.042">void addPrompt(PromptRegistration promptRegistration)</text><ellipse cx="1775.5" cy="782.9922" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="288.0049" x="1784.5" y="786.3389">void removePrompt(String promptName)</text><ellipse cx="1775.5" cy="799.2891" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="230.0635" x="1784.5" y="802.6357">void notifyPromptsListChanged()</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1784.5" y="818.9326">&#160;</text><ellipse cx="1775.5" cy="831.8828" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="457.8643" x="1784.5" y="835.2295">void loggingNotification(LoggingMessageNotification notification)</text><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="4.4502" x="1784.5" y="851.5264">&#160;</text><ellipse cx="1775.5" cy="864.4766" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="497.2119" x="1784.5" y="867.8232">CreateMessageResult createMessage(CreateMessageRequest request)</text></g><!--class StdioClientTransport--><g id="elem_StdioClientTransport"><rect codeLine="145" fill="#F1F1F1" height="80.5938" id="StdioClientTransport" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="412.8525" x="639.5" y="1529"/><ellipse cx="769.6768" cy="1545" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M772.6455,1550.6406 Q772.0674,1550.9375 771.4268,1551.0781 Q770.7861,1551.2344 770.083,1551.2344 Q767.583,1551.2344 766.2549,1549.5938 Q764.9424,1547.9375 764.9424,1544.8125 Q764.9424,1541.6875 766.2549,1540.0313 Q767.583,1538.375 770.083,1538.375 Q770.7861,1538.375 771.4268,1538.5313 Q772.083,1538.6875 772.6455,1538.9844 L772.6455,1541.7031 Q772.0205,1541.125 771.4268,1540.8594 Q770.833,1540.5781 770.208,1540.5781 Q768.8643,1540.5781 768.1768,1541.6563 Q767.4893,1542.7188 767.4893,1544.8125 Q767.4893,1546.9063 768.1768,1547.9844 Q768.8643,1549.0469 770.208,1549.0469 Q770.833,1549.0469 771.4268,1548.7813 Q772.0205,1548.5 772.6455,1547.9219 L772.6455,1550.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="143.999" x="790.1768" y="1549.8467">StdioClientTransport</text><line style="stroke:#181818;stroke-width:0.5;" x1="640.5" x2="1051.3525" y1="1561" y2="1561"/><line style="stroke:#181818;stroke-width:0.5;" x1="640.5" x2="1051.3525" y1="1569" y2="1569"/><ellipse cx="650.5" cy="1582.6484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="386.8525" x="659.5" y="1585.9951">void setErrorHandler(Consumer&lt;String&gt; errorHandler)</text><ellipse cx="650.5" cy="1598.9453" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="247.3584" x="659.5" y="1602.292">Sinks.Many&lt;String&gt; getErrorSink()</text></g><!--class ClientMcpTransport--><g id="elem_ClientMcpTransport"><rect fill="#F1F1F1" height="48" id="ClientMcpTransport" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="168.9443" x="584.5" y="1420"/><ellipse cx="599.5" cy="1436" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M595.4219,1431.7656 L595.4219,1429.6094 L602.8125,1429.6094 L602.8125,1431.7656 L600.3438,1431.7656 L600.3438,1439.8438 L602.8125,1439.8438 L602.8125,1442 L595.4219,1442 L595.4219,1439.8438 L597.8906,1439.8438 L597.8906,1431.7656 L595.4219,1431.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="136.9443" x="613.5" y="1440.8467">ClientMcpTransport</text><line style="stroke:#181818;stroke-width:0.5;" x1="585.5" x2="752.4443" y1="1452" y2="1452"/><line style="stroke:#181818;stroke-width:0.5;" x1="585.5" x2="752.4443" y1="1460" y2="1460"/></g><!--class StdioServerTransport--><g id="elem_StdioServerTransport"><rect codeLine="150" fill="#F1F1F1" height="48" id="StdioServerTransport" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="181.3789" x="1433.5" y="1545.5"/><ellipse cx="1448.5" cy="1561.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1451.4688,1567.1406 Q1450.8906,1567.4375 1450.25,1567.5781 Q1449.6094,1567.7344 1448.9063,1567.7344 Q1446.4063,1567.7344 1445.0781,1566.0938 Q1443.7656,1564.4375 1443.7656,1561.3125 Q1443.7656,1558.1875 1445.0781,1556.5313 Q1446.4063,1554.875 1448.9063,1554.875 Q1449.6094,1554.875 1450.25,1555.0313 Q1450.9063,1555.1875 1451.4688,1555.4844 L1451.4688,1558.2031 Q1450.8438,1557.625 1450.25,1557.3594 Q1449.6563,1557.0781 1449.0313,1557.0781 Q1447.6875,1557.0781 1447,1558.1563 Q1446.3125,1559.2188 1446.3125,1561.3125 Q1446.3125,1563.4063 1447,1564.4844 Q1447.6875,1565.5469 1449.0313,1565.5469 Q1449.6563,1565.5469 1450.25,1565.2813 Q1450.8438,1565 1451.4688,1564.4219 L1451.4688,1567.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="149.3789" x="1462.5" y="1566.3467">StdioServerTransport</text><line style="stroke:#181818;stroke-width:0.5;" x1="1434.5" x2="1613.8789" y1="1577.5" y2="1577.5"/><line style="stroke:#181818;stroke-width:0.5;" x1="1434.5" x2="1613.8789" y1="1585.5" y2="1585.5"/></g><!--class ServerMcpTransport--><g id="elem_ServerMcpTransport"><rect fill="#F1F1F1" height="48" id="ServerMcpTransport" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="174.3242" x="1437" y="1420"/><ellipse cx="1452" cy="1436" fill="#B4A7E5" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1447.9219,1431.7656 L1447.9219,1429.6094 L1455.3125,1429.6094 L1455.3125,1431.7656 L1452.8438,1431.7656 L1452.8438,1439.8438 L1455.3125,1439.8438 L1455.3125,1442 L1447.9219,1442 L1447.9219,1439.8438 L1450.3906,1439.8438 L1450.3906,1431.7656 L1447.9219,1431.7656 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" font-style="italic" lengthAdjust="spacing" textLength="142.3242" x="1466" y="1440.8467">ServerMcpTransport</text><line style="stroke:#181818;stroke-width:0.5;" x1="1438" x2="1610.3242" y1="1452" y2="1452"/><line style="stroke:#181818;stroke-width:0.5;" x1="1438" x2="1610.3242" y1="1460" y2="1460"/></g><!--class HttpServletSseServerTransport--><g id="elem_HttpServletSseServerTransport"><rect codeLine="154" fill="#F1F1F1" height="48" id="HttpServletSseServerTransport" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="250.3809" x="1650" y="1545.5"/><ellipse cx="1665" cy="1561.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1667.9688,1567.1406 Q1667.3906,1567.4375 1666.75,1567.5781 Q1666.1094,1567.7344 1665.4063,1567.7344 Q1662.9063,1567.7344 1661.5781,1566.0938 Q1660.2656,1564.4375 1660.2656,1561.3125 Q1660.2656,1558.1875 1661.5781,1556.5313 Q1662.9063,1554.875 1665.4063,1554.875 Q1666.1094,1554.875 1666.75,1555.0313 Q1667.4063,1555.1875 1667.9688,1555.4844 L1667.9688,1558.2031 Q1667.3438,1557.625 1666.75,1557.3594 Q1666.1563,1557.0781 1665.5313,1557.0781 Q1664.1875,1557.0781 1663.5,1558.1563 Q1662.8125,1559.2188 1662.8125,1561.3125 Q1662.8125,1563.4063 1663.5,1564.4844 Q1664.1875,1565.5469 1665.5313,1565.5469 Q1666.1563,1565.5469 1666.75,1565.2813 Q1667.3438,1565 1667.9688,1564.4219 L1667.9688,1567.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="218.3809" x="1679" y="1566.3467">HttpServletSseServerTransport</text><line style="stroke:#181818;stroke-width:0.5;" x1="1651" x2="1899.3809" y1="1577.5" y2="1577.5"/><line style="stroke:#181818;stroke-width:0.5;" x1="1651" x2="1899.3809" y1="1585.5" y2="1585.5"/></g><!--class HttpClientSseClientTransport--><g id="elem_HttpClientSseClientTransport"><rect codeLine="158" fill="#F1F1F1" height="48" id="HttpClientSseClientTransport" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="235.998" x="107" y="1545.5"/><ellipse cx="122" cy="1561.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M124.9688,1567.1406 Q124.3906,1567.4375 123.75,1567.5781 Q123.1094,1567.7344 122.4063,1567.7344 Q119.9063,1567.7344 118.5781,1566.0938 Q117.2656,1564.4375 117.2656,1561.3125 Q117.2656,1558.1875 118.5781,1556.5313 Q119.9063,1554.875 122.4063,1554.875 Q123.1094,1554.875 123.75,1555.0313 Q124.4063,1555.1875 124.9688,1555.4844 L124.9688,1558.2031 Q124.3438,1557.625 123.75,1557.3594 Q123.1563,1557.0781 122.5313,1557.0781 Q121.1875,1557.0781 120.5,1558.1563 Q119.8125,1559.2188 119.8125,1561.3125 Q119.8125,1563.4063 120.5,1564.4844 Q121.1875,1565.5469 122.5313,1565.5469 Q123.1563,1565.5469 123.75,1565.2813 Q124.3438,1565 124.9688,1564.4219 L124.9688,1567.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="203.998" x="136" y="1566.3467">HttpClientSseClientTransport</text><line style="stroke:#181818;stroke-width:0.5;" x1="108" x2="341.998" y1="1577.5" y2="1577.5"/><line style="stroke:#181818;stroke-width:0.5;" x1="108" x2="341.998" y1="1585.5" y2="1585.5"/></g><!--class WebFluxSseClientTransport--><g id="elem_WebFluxSseClientTransport"><rect codeLine="162" fill="#F1F1F1" height="48" id="WebFluxSseClientTransport" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="225.5186" x="378" y="1545.5"/><ellipse cx="393" cy="1561.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M395.9688,1567.1406 Q395.3906,1567.4375 394.75,1567.5781 Q394.1094,1567.7344 393.4063,1567.7344 Q390.9063,1567.7344 389.5781,1566.0938 Q388.2656,1564.4375 388.2656,1561.3125 Q388.2656,1558.1875 389.5781,1556.5313 Q390.9063,1554.875 393.4063,1554.875 Q394.1094,1554.875 394.75,1555.0313 Q395.4063,1555.1875 395.9688,1555.4844 L395.9688,1558.2031 Q395.3438,1557.625 394.75,1557.3594 Q394.1563,1557.0781 393.5313,1557.0781 Q392.1875,1557.0781 391.5,1558.1563 Q390.8125,1559.2188 390.8125,1561.3125 Q390.8125,1563.4063 391.5,1564.4844 Q392.1875,1565.5469 393.5313,1565.5469 Q394.1563,1565.5469 394.75,1565.2813 Q395.3438,1565 395.9688,1564.4219 L395.9688,1567.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="193.5186" x="407" y="1566.3467">WebFluxSseClientTransport</text><line style="stroke:#181818;stroke-width:0.5;" x1="379" x2="602.5186" y1="1577.5" y2="1577.5"/><line style="stroke:#181818;stroke-width:0.5;" x1="379" x2="602.5186" y1="1585.5" y2="1585.5"/></g><!--class WebFluxSseServerTransport--><g id="elem_WebFluxSseServerTransport"><rect codeLine="166" fill="#F1F1F1" height="64.2969" id="WebFluxSseServerTransport" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="309.9307" x="1935" y="1537.5"/><ellipse cx="1986.2661" cy="1553.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1989.2349,1559.1406 Q1988.6567,1559.4375 1988.0161,1559.5781 Q1987.3755,1559.7344 1986.6724,1559.7344 Q1984.1724,1559.7344 1982.8442,1558.0938 Q1981.5317,1556.4375 1981.5317,1553.3125 Q1981.5317,1550.1875 1982.8442,1548.5313 Q1984.1724,1546.875 1986.6724,1546.875 Q1987.3755,1546.875 1988.0161,1547.0313 Q1988.6724,1547.1875 1989.2349,1547.4844 L1989.2349,1550.2031 Q1988.6099,1549.625 1988.0161,1549.3594 Q1987.4224,1549.0781 1986.7974,1549.0781 Q1985.4536,1549.0781 1984.7661,1550.1563 Q1984.0786,1551.2188 1984.0786,1553.3125 Q1984.0786,1555.4063 1984.7661,1556.4844 Q1985.4536,1557.5469 1986.7974,1557.5469 Q1987.4224,1557.5469 1988.0161,1557.2813 Q1988.6099,1557 1989.2349,1556.4219 L1989.2349,1559.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="198.8984" x="2006.7661" y="1558.3467">WebFluxSseServerTransport</text><line style="stroke:#181818;stroke-width:0.5;" x1="1936" x2="2243.9307" y1="1569.5" y2="1569.5"/><line style="stroke:#181818;stroke-width:0.5;" x1="1936" x2="2243.9307" y1="1577.5" y2="1577.5"/><ellipse cx="1946" cy="1591.1484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="283.9307" x="1955" y="1594.4951">RouterFunction&lt;?&gt; getRouterFunction()</text></g><!--class WebMvcSseServerTransport--><g id="elem_WebMvcSseServerTransport"><rect codeLine="170" fill="#F1F1F1" height="64.2969" id="WebMvcSseServerTransport" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="309.9307" x="1088" y="1537.5"/><ellipse cx="1139.7856" cy="1553.5" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1142.7544,1559.1406 Q1142.1763,1559.4375 1141.5356,1559.5781 Q1140.895,1559.7344 1140.1919,1559.7344 Q1137.6919,1559.7344 1136.3638,1558.0938 Q1135.0513,1556.4375 1135.0513,1553.3125 Q1135.0513,1550.1875 1136.3638,1548.5313 Q1137.6919,1546.875 1140.1919,1546.875 Q1140.895,1546.875 1141.5356,1547.0313 Q1142.1919,1547.1875 1142.7544,1547.4844 L1142.7544,1550.2031 Q1142.1294,1549.625 1141.5356,1549.3594 Q1140.9419,1549.0781 1140.3169,1549.0781 Q1138.9731,1549.0781 1138.2856,1550.1563 Q1137.5981,1551.2188 1137.5981,1553.3125 Q1137.5981,1555.4063 1138.2856,1556.4844 Q1138.9731,1557.5469 1140.3169,1557.5469 Q1140.9419,1557.5469 1141.5356,1557.2813 Q1142.1294,1557 1142.7544,1556.4219 L1142.7544,1559.1406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="197.8594" x="1160.2856" y="1558.3467">WebMvcSseServerTransport</text><line style="stroke:#181818;stroke-width:0.5;" x1="1089" x2="1396.9307" y1="1569.5" y2="1569.5"/><line style="stroke:#181818;stroke-width:0.5;" x1="1089" x2="1396.9307" y1="1577.5" y2="1577.5"/><ellipse cx="1099" cy="1591.1484" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="283.9307" x="1108" y="1594.4951">RouterFunction&lt;?&gt; getRouterFunction()</text></g><!--class McpSchema--><g id="elem_McpSchema"><rect codeLine="176" fill="#F1F1F1" height="162.0781" id="McpSchema" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="357.0918" x="778.5" y="7"/><ellipse cx="910.3208" cy="23" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M913.2896,28.6406 Q912.7114,28.9375 912.0708,29.0781 Q911.4302,29.2344 910.7271,29.2344 Q908.2271,29.2344 906.8989,27.5938 Q905.5864,25.9375 905.5864,22.8125 Q905.5864,19.6875 906.8989,18.0313 Q908.2271,16.375 910.7271,16.375 Q911.4302,16.375 912.0708,16.5313 Q912.7271,16.6875 913.2896,16.9844 L913.2896,19.7031 Q912.6646,19.125 912.0708,18.8594 Q911.4771,18.5781 910.8521,18.5781 Q909.5083,18.5781 908.8208,19.6563 Q908.1333,20.7188 908.1333,22.8125 Q908.1333,24.9063 908.8208,25.9844 Q909.5083,27.0469 910.8521,27.0469 Q911.4771,27.0469 912.0708,26.7813 Q912.6646,26.5 913.2896,25.9219 L913.2896,28.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="84.9502" x="930.8208" y="27.8467">McpSchema</text><line style="stroke:#181818;stroke-width:0.5;" x1="779.5" x2="1134.5918" y1="39" y2="39"/><ellipse cx="789.5" cy="52.6484" fill="none" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="117.0176" x="798.5" y="55.9951">class ErrorCodes</text><ellipse cx="789.5" cy="68.9453" fill="none" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="124.3867" x="798.5" y="72.292">interface Request</text><ellipse cx="789.5" cy="85.2422" fill="none" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="190.7021" x="798.5" y="88.5889">interface JSONRPCMessage</text><ellipse cx="789.5" cy="101.5391" fill="none" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="195.002" x="798.5" y="104.8857">interface ResourceContents</text><ellipse cx="789.5" cy="117.8359" fill="none" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="122.5684" x="798.5" y="121.1826">interface Content</text><ellipse cx="789.5" cy="134.1328" fill="none" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="194.4619" x="798.5" y="137.4795">interface ServerCapabilities</text><line style="stroke:#181818;stroke-width:0.5;" x1="779.5" x2="1134.5918" y1="144.7813" y2="144.7813"/><ellipse cx="789.5" cy="158.4297" fill="#84BE84" rx="3" ry="3" style="stroke:#038048;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" text-decoration="underline" textLength="331.0918" x="798.5" y="161.7764">JSONRPCMessage deserializeJsonRpcMessage()</text></g><!--class McpError--><g id="elem_McpError"><rect codeLine="186" fill="#F1F1F1" height="48" id="McpError" rx="2.5" ry="2.5" style="stroke:#181818;stroke-width:0.5;" width="95.3418" x="1170.5" y="64"/><ellipse cx="1185.5" cy="80" fill="#ADD1B2" rx="11" ry="11" style="stroke:#181818;stroke-width:1;"/><path d="M1188.4688,85.6406 Q1187.8906,85.9375 1187.25,86.0781 Q1186.6094,86.2344 1185.9063,86.2344 Q1183.4063,86.2344 1182.0781,84.5938 Q1180.7656,82.9375 1180.7656,79.8125 Q1180.7656,76.6875 1182.0781,75.0313 Q1183.4063,73.375 1185.9063,73.375 Q1186.6094,73.375 1187.25,73.5313 Q1187.9063,73.6875 1188.4688,73.9844 L1188.4688,76.7031 Q1187.8438,76.125 1187.25,75.8594 Q1186.6563,75.5781 1186.0313,75.5781 Q1184.6875,75.5781 1184,76.6563 Q1183.3125,77.7188 1183.3125,79.8125 Q1183.3125,81.9063 1184,82.9844 Q1184.6875,84.0469 1186.0313,84.0469 Q1186.6563,84.0469 1187.25,83.7813 Q1187.8438,83.5 1188.4688,82.9219 L1188.4688,85.6406 Z " fill="#000000"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacing" textLength="63.3418" x="1199.5" y="84.8467">McpError</text><line style="stroke:#181818;stroke-width:0.5;" x1="1171.5" x2="1264.8418" y1="96" y2="96"/><line style="stroke:#181818;stroke-width:0.5;" x1="1171.5" x2="1264.8418" y1="104" y2="104"/></g><!--reverse link ClientMcpTransport to StdioClientTransport--><g id="link_ClientMcpTransport_StdioClientTransport"><path d="M717.0526,1478.5288 C741.4926,1495.5788 760.51,1508.85 789.18,1528.86 " fill="none" id="ClientMcpTransport-backto-StdioClientTransport" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="none" points="702.29,1468.23,713.6197,1483.4497,720.4855,1473.6079,702.29,1468.23" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link ServerMcpTransport to StdioServerTransport--><g id="link_ServerMcpTransport_StdioServerTransport"><path d="M1524,1486.23 C1524,1508.31 1524,1523.33 1524,1545.37 " fill="none" id="ServerMcpTransport-backto-StdioServerTransport" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="none" points="1524,1468.23,1518,1486.23,1530,1486.23,1524,1468.23" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link ServerMcpTransport to HttpServletSseServerTransport--><g id="link_ServerMcpTransport_HttpServletSseServerTransport"><path d="M1587.0691,1476.0303 C1631.8991,1498.1003 1682.99,1523.23 1727.89,1545.32 " fill="none" id="ServerMcpTransport-backto-HttpServletSseServerTransport" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="none" points="1570.92,1468.08,1584.419,1481.4133,1589.7192,1470.6472,1570.92,1468.08" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link ClientMcpTransport to HttpClientSseClientTransport--><g id="link_ClientMcpTransport_HttpClientSseClientTransport"><path d="M566.9043,1472.2225 C504.7443,1488.8925 435.71,1507.64 360,1529 C341.43,1534.24 321.47,1540.01 302.74,1545.48 " fill="none" id="ClientMcpTransport-backto-HttpClientSseClientTransport" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="none" points="584.29,1467.56,565.3502,1466.4272,568.4585,1478.0177,584.29,1467.56" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link ClientMcpTransport to WebFluxSseClientTransport--><g id="link_ClientMcpTransport_WebFluxSseClientTransport"><path d="M620.7316,1478.4917 C588.9116,1500.5717 556.1,1523.33 524.33,1545.37 " fill="none" id="ClientMcpTransport-backto-WebFluxSseClientTransport" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="none" points="635.52,1468.23,617.311,1473.5623,624.1522,1483.4212,635.52,1468.23" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link ServerMcpTransport to WebFluxSseServerTransport--><g id="link_ServerMcpTransport_WebFluxSseServerTransport"><path d="M1628.947,1466.9032 C1708.847,1483.6532 1812.67,1505.68 1918,1529 C1930.26,1531.72 1943.01,1534.57 1955.77,1537.46 " fill="none" id="ServerMcpTransport-backto-WebFluxSseServerTransport" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="none" points="1611.33,1463.21,1627.716,1472.7755,1630.1781,1461.0308,1611.33,1463.21" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link ServerMcpTransport to WebMvcSseServerTransport--><g id="link_ServerMcpTransport_WebMvcSseServerTransport"><path d="M1455.0021,1475.3242 C1410.4421,1494.9142 1362.61,1515.93 1313.56,1537.49 " fill="none" id="ServerMcpTransport-backto-WebMvcSseServerTransport" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="none" points="1471.48,1468.08,1452.5874,1469.8316,1457.4168,1480.8168,1471.48,1468.08" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link McpTransport to ClientMcpTransport--><g id="link_McpTransport_ClientMcpTransport"><path codeLine="190" d="M979.3828,1230.2564 C887.2628,1293.3964 765.82,1376.64 702.99,1419.7 " fill="none" id="McpTransport-backto-ClientMcpTransport" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="none" points="994.23,1220.08,975.9906,1225.3074,982.7749,1235.2055,994.23,1220.08" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link McpTransport to ServerMcpTransport--><g id="link_McpTransport_ServerMcpTransport"><path codeLine="191" d="M1199.0331,1229.9494 C1295.4731,1293.1794 1423.15,1376.88 1488.72,1419.87 " fill="none" id="McpTransport-backto-ServerMcpTransport" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="none" points="1183.98,1220.08,1195.7433,1234.9671,1202.3229,1224.9317,1183.98,1220.08" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link McpSession to DefaultMcpSession--><g id="link_McpSession_DefaultMcpSession"><path codeLine="193" d="M1087,377.12 C1087,448.45 1087,552.39 1087,614.94 " fill="none" id="McpSession-backto-DefaultMcpSession" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="none" points="1087,359.12,1081,377.12,1093,377.12,1087,359.12" style="stroke:#181818;stroke-width:1;"/></g><!--link DefaultMcpSession to McpAsyncClient--><g id="link_DefaultMcpSession_McpAsyncClient"><path codeLine="194" d="M1033.51,696.2 C956.43,753.41 818.5558,855.728 687.0458,953.338 " fill="none" id="DefaultMcpSession-to-McpAsyncClient" style="stroke:#181818;stroke-width:1;"/><polygon fill="none" points="677.41,960.49,684.6119,960.126,687.0458,953.338,679.8439,953.7021,677.41,960.49" style="stroke:#181818;stroke-width:1;"/></g><!--link DefaultMcpSession to McpAsyncServer--><g id="link_DefaultMcpSession_McpAsyncServer"><path codeLine="195" d="M1141.45,696.2 C1217.62,751.74 1351.6438,849.46 1482.4538,944.84 " fill="none" id="DefaultMcpSession-to-McpAsyncServer" style="stroke:#181818;stroke-width:1;"/><polygon fill="none" points="1492.15,951.91,1489.6586,945.143,1482.4538,944.84,1484.9453,951.6071,1492.15,951.91" style="stroke:#181818;stroke-width:1;"/></g><!--link McpClient to McpAsyncClient--><g id="link_McpClient_McpAsyncClient"><path codeLine="197" d="M496.28,334.76 C505.99,361.24 518.75,400.46 524,436 C552.5,629.02 559.77,683.2 524,875 C518.75,903.13 512.6628,926.3731 503.3728,954.4831 " fill="none" id="McpClient-to-McpAsyncClient" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="#181818" points="501.49,960.18,508.1121,952.8898,503.059,955.4325,500.5162,950.3794,501.49,960.18" style="stroke:#181818;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="48.3247" x="550" y="660.0669">creates</text></g><!--link McpClient to McpSyncClient--><g id="link_McpClient_McpSyncClient"><path codeLine="198" d="M463.86,334.65 C444.76,364.18 417.6084,406.1618 385.9184,455.1618 " fill="none" id="McpClient-to-McpSyncClient" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="#181818" points="382.66,460.2,390.9063,454.815,385.3753,456.0015,384.1888,450.4705,382.66,460.2" style="stroke:#181818;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="48.3247" x="425" y="402.0669">creates</text></g><!--link McpSyncClient to McpAsyncClient--><g id="link_McpSyncClient_McpAsyncClient"><path codeLine="199" d="M319.04,850.82 C330.5,886.72 340.6454,918.4842 352.1054,954.3842 " fill="none" id="McpSyncClient-to-McpAsyncClient" style="stroke:#181818;stroke-width:1;"/><polygon fill="#181818" points="353.93,960.1,355.0036,950.3098,352.4095,955.3368,347.3825,952.7427,353.93,960.1" style="stroke:#181818;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="81.1294" x="343" y="918.0669">delegates to</text></g><!--link McpServer to McpAsyncServer--><g id="link_McpServer_McpAsyncServer"><path codeLine="201" d="M1771.45,334.52 C1745.02,358.95 1711.8,395.55 1697,436 C1636.02,602.61 1663.2546,798.7393 1701.1646,946.0493 " fill="none" id="McpServer-to-McpAsyncServer" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="#181818" points="1702.66,951.86,1704.2907,942.1471,1701.4139,947.0178,1696.5432,944.1409,1702.66,951.86" style="stroke:#181818;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="48.3247" x="1698" y="660.0669">creates</text></g><!--link McpServer to McpSyncServer--><g id="link_McpServer_McpSyncServer"><path codeLine="202" d="M1828.26,334.65 C1843.45,359.23 1862.9848,390.8266 1887.6348,430.6966 " fill="none" id="McpServer-to-McpSyncServer" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="#181818" points="1890.79,435.8,1889.4594,426.0414,1888.1607,431.5472,1882.6549,430.2484,1890.79,435.8" style="stroke:#181818;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="48.3247" x="1868" y="402.0669">creates</text></g><!--reverse link McpSyncServer to McpAsyncServer--><g id="link_McpSyncServer_McpAsyncServer"><path codeLine="203" d="M1908.0431,885.9621 C1894.9531,911.4221 1887.13,926.64 1874.16,951.87 " fill="none" id="McpSyncServer-backto-McpAsyncServer" style="stroke:#181818;stroke-width:1;"/><polygon fill="none" points="1913.53,875.29,1907.2292,878.7971,1908.0431,885.9621,1914.3439,882.455,1913.53,875.29" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link DefaultMcpSession to McpTransport--><g id="link_DefaultMcpSession_McpTransport"><path codeLine="205" d="M1087,708.41 C1087,794.62 1087,986.83 1087,1091 " fill="none" id="DefaultMcpSession-backto-McpTransport" style="stroke:#181818;stroke-width:1;"/><polygon fill="none" points="1087,696.41,1083,702.41,1087,708.41,1091,702.41,1087,696.41" style="stroke:#181818;stroke-width:1;"/></g><!--reverse link McpSchema to McpSession--><g id="link_McpSchema_McpSession"><path codeLine="206" d="M1009.0909,174.1484 C1024.7809,199.7984 1038.67,222.5 1053.05,246 " fill="none" id="McpSchema-backto-McpSession" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="#181818" points="1005.96,169.03,1007.2441,178.7948,1008.5691,173.2953,1014.0686,174.6203,1005.96,169.03" style="stroke:#181818;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="29.7832" x="1035" y="212.0669">uses</text></g><!--link McpError to McpSession--><g id="link_McpError_McpSession"><path codeLine="207" d="M1203.73,112.15 C1184.1,143.99 1151.2982,197.1923 1124.4682,240.7223 " fill="none" id="McpError-to-McpSession" style="stroke:#181818;stroke-width:1;stroke-dasharray:7.0,7.0;"/><polygon fill="#181818" points="1121.32,245.83,1129.4474,240.2672,1123.9435,241.5736,1122.6371,236.0696,1121.32,245.83" style="stroke:#181818;stroke-width:1;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacing" textLength="44.04" x="1149" y="212.0669">throws</text></g><!--SRC=[tLZTSk8s5BxdANGNxpJa0NOOEYrjDjb9iXbWssjX7y2ja5n9Jfkc- -xLhot9me6qlTZEC82FllEZe_FfoBf02yO1tL89V8jB49FQ_qNtPRFcc8g6SObU9WXzSyyec_t4wcgEIaOfbBAuRcafQqPdPkpZwjF3yd5n3qBW7SmlKKuwHtjCAmByNM9J0AqSg8XrqC7-6eqd1KObOGAAr8AHVk0g-crBoBCTdwtdQ8rQUi1Sx36vvknGVDhyeg975PRu4gsV6rZ25ZZG4ZHQMi6xoPT9d8-L2aDDrcP38utljGrz2R-r86InahocEnMotBFg4ZbNt8u-OeuFOipdC2SFJDRRkiGwpwdrUzpChCXxcgF6-3WfDr4krMmlN3qS6W87eDBuH0k2XL2rKUfVz0DiSKjaceu-KIuHDtQLux5mBXvW_P9Q-KjSAHzERboy21rG-GLztzQa0evQeIPUM4XKhjB_CxFgZ7qddcaPMZJvkQMrh36f_rGB8DJCvgZk93PDA-1Fm1UumPkQg24GQ0ToABNe97iTyfPkcKNO459xG-anUnRPspkN4cQyAnUTmrGDLzMICLdeByJLLmhWS61FO32k93NgCwy7G2KMTxMG2qNx7SJLt8CNc90rOyGlGUcUYtx0L9KdKg-fRMk0yAh42ueOfCPPozKc5m8xOG1U0gwyt6Dv82jYm4XS7hG6jOPOvbrtWuDL4Er6bBpUGETvrKkEkBMQBHtgVWvb8q4_gXsoaL6eJXG4-06SqCDsNb04GwCn2f1tRBTJjNC7Jq2Ay04H_HspTvMGD-NjLq_RZIfQm3JFMTT7UhOQxJnFpT0uGotaLMx6j3oDbGaXKpvcg9XZuf24vt5mX7vZx3DvEPDtHkSDZ1kGRSYrX6QdiipEuZH-NI1RvyFUw-HZlTT2JkwzWSN_OLzLdmkaArqTowWwhchRmxvg-6EvirFBpQrtcGZo_TReEZPhZGTyW1H9DOcV3U42QlS1-keS3JAWcQTO42ntmflyGTxjF1rttExZdZDd11jojm5amxR5FXgqOuQdHH1mCn3bOtOMNCHMwtYE7LEpuJgo9Nh4IgDzrY7TgquuPDsuXyxo2gkw3KqlrBuemUbtvwTGM2IGPCD8csp8-uIvYYJ1ev_kLqVa_lb42ljcUvoy8HU2_dkKiGRpHgvshVzibeOdWrVu6GJu1cRw4C_cP13XA1k3y4xaeT7ujncO8T-vt5C5rpNFk9HzBrx9gcQsfdjexQ42PTw0Nomn_K7WHyuPTuLGB9JbzW3StHpC4GIZzkHSqr_5bLhHvmwpSqRqK17i6IOsozziR9aJmelMTketKffp1b6RW81_PiUcGPavFrMzePzXSqtQFywtahLnxeJqmPUr4dXtiVQjINqi3FElVDq_bV-nAtr8BFTmm4WL2J9ra3akMeInR8K6k62LGkLkY69XVEMNAjWDI5Mkr3EAHvqq5WwRJtICJIuOLOZ0xRlZHy7eiYazVUFsQAV-Occx169fkgyRCOdIE_lhwYfJ9HdGS6qca-1kpM06LswJuINSTCgIAwvNGDQnSURUgg-kvkajsNktcOaXR7K45SYdqup12WZiD5ZrGpTLSxG8lJjHR3U2hOwXUZcRJWUJ7QR4eQ8bSfKyKwaFaE_LEuzrOgl0Hz8Doprdluk_0G00]--></g></svg>