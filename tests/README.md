# Test Suite for SUSE AIR

This directory contains comprehensive tests for the SUSE AIR OpenAPI to MCP converter project.

## Structure

```
tests/
├── unit/                   # Unit tests for individual components
│   ├── schema/            # Schema conversion and execution tests
│   ├── models/            # Data model tests
│   └── db/                # Database connection tests
├── integration/           # Integration tests with real database
├── testdata/             # Test data files
│   ├── openapi_specs/    # Sample OpenAPI specifications
│   └── expected_outputs/ # Expected conversion results
└── README.md             # This file
```

## Running Tests

### Prerequisites

#### For Unit Tests Only
- Go 1.24.4 or later
- No additional dependencies required

#### For Integration Tests
Choose one of the following options:

**Option 1: Docker (Recommended)**
- Docker installed and running
- Tests will automatically create PostgreSQL containers

**Option 2: Existing Database**
- Set `TEST_DATABASE_URL` environment variable
- Example: `export TEST_DATABASE_URL="postgres://user:pass@localhost:5432/testdb?sslmode=disable"`

### Running Tests

#### Quick Start
```bash
# Run all tests (requires Docker)
make test

# Run only unit tests (no Docker required)
make test-unit

# Run tests without Docker dependencies
make test-no-docker
```

#### Detailed Commands

```bash
# Unit tests only
go test -v ./tests/unit/...

# Integration tests (requires Docker or TEST_DATABASE_URL)
go test -v ./tests/integration/...

# All tests with coverage
make test-coverage

# Skip database tests entirely
SKIP_DB_TESTS=true go test -v ./tests/...
```

## Test Categories

### Unit Tests

**Schema Converter Tests** (`tests/unit/schema/converter_test.go`)
- OpenAPI to MCP tool conversion
- $ref resolution and circular reference handling
- Parameter flattening (path, query, header)
- Body mapping strategies (wrapped vs merged)
- Edge cases (missing operationId, complex schemas)

**Request Executor Tests** (`tests/unit/schema/executor_test.go`)
- HTTP request building from MCP arguments
- Parameter distribution to correct locations
- Body reconstruction (wrapped and merged strategies)
- URL building and query parameter handling
- Error handling for invalid data

**Model Tests** (`tests/unit/models/`)
- JSON marshaling/unmarshaling for all data structures
- Complex nested schema handling
- OpenAPI specification parsing
- MCP tool definition validation

**Database Connection Tests** (`tests/unit/db/connection_test.go`)
- Connection string validation
- Error handling for invalid connections
- Basic connectivity testing

### Integration Tests

**Database Operations** (`tests/integration/database_test.go`)
- Full CRUD operations with PostgreSQL
- Complex JSON schema storage and retrieval
- Foreign key constraint testing
- Transaction handling

**End-to-End Tests** (`tests/integration/end_to_end_test.go`)
- Complete CLI command testing
- Convert command with real OpenAPI specs
- Execute command with mock HTTP server
- Error handling and edge cases
- Application binary building and testing

## Test Data

### OpenAPI Specifications

**simple.json**
- Basic endpoints with different parameter types
- Simple request/response schemas
- Good for testing fundamental conversion logic

**complex_with_refs.json**
- Complex schemas with $ref references
- Nested object structures
- Circular reference scenarios
- Tests schema resolution capabilities

**edge_cases.json**
- Missing operationId fields
- Circular references
- Deep nesting
- Mixed parameter types
- Edge cases that could break conversion

## Environment Variables

- `TEST_DATABASE_URL`: Connection string for existing test database
- `SKIP_DB_TESTS`: Set to "true" to skip all database-dependent tests
- `DOCKER_HOST`: Docker daemon connection (for dockertest)

## CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run tests
  run: |
    # Unit tests (always run)
    make test-unit
    
    # Integration tests with Docker
    make test-integration
  env:
    SKIP_DB_TESTS: false
```

### Local Development
```bash
# Start development database
make dev-db

# Run tests against development database
export TEST_DATABASE_URL="postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable"
make test

# Stop development database
make stop-dev-db
```

## Writing New Tests

### Unit Tests
1. Place in appropriate subdirectory under `tests/unit/`
2. Use `github.com/stretchr/testify` for assertions
3. Mock external dependencies
4. Focus on single component behavior

### Integration Tests
1. Place in `tests/integration/`
2. Use `SetupTestDatabase()` helper for database setup
3. Clean up resources in defer statements
4. Test complete workflows

### Test Data
1. Add new OpenAPI specs to `tests/testdata/openapi_specs/`
2. Keep specs focused on specific test scenarios
3. Document the purpose of each test file

## Coverage Goals

- **Unit Tests**: 90%+ coverage for core logic
- **Integration Tests**: Complete workflow coverage
- **Error Handling**: All error paths tested
- **Edge Cases**: All identified edge cases covered

## Troubleshooting

### Docker Issues
```bash
# Check Docker availability
docker version

# Manual container cleanup
docker ps -a | grep postgres
docker rm -f <container-id>
```

### Database Connection Issues
```bash
# Test database connection manually
psql $TEST_DATABASE_URL -c "SELECT 1;"

# Check if port is in use
lsof -i :5432
```

### Test Failures
```bash
# Run specific test
go test -v -run TestSpecificTest ./tests/unit/schema/

# Run with detailed output
go test -v -timeout 30s ./tests/integration/

# Debug database state
export TEST_DATABASE_URL="your-connection-string"
psql $TEST_DATABASE_URL -c "\dt"  # List tables
```