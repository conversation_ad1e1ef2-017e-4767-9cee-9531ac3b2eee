{"openapi": "3.0.0", "info": {"title": "Simple Test API", "version": "1.0.0"}, "paths": {"/hello": {"post": {"operationId": "sayHello", "summary": "Say hello", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "string", "description": "Name of the person to greet"}}}}}}, "/users/{userId}": {"get": {"operationId": "getUser", "summary": "Get user by ID", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "User ID"}, {"name": "include", "in": "query", "required": false, "schema": {"type": "string"}, "description": "Include additional fields"}]}}, "/users": {"post": {"operationId": "createUser", "summary": "Create a new user", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "User's full name"}, "email": {"type": "string", "description": "User's email address"}}, "required": ["name", "email"]}}}}}}}}