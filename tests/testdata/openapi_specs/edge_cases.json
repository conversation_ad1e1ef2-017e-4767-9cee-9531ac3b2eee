{"openapi": "3.0.0", "info": {"title": "Edge Cases API", "version": "1.0.0"}, "components": {"schemas": {"CircularRef": {"type": "object", "properties": {"name": {"type": "string"}, "child": {"$ref": "#/components/schemas/CircularRef"}}}, "DeepNested": {"type": "object", "properties": {"level1": {"$ref": "#/components/schemas/Level1"}}}, "Level1": {"type": "object", "properties": {"level2": {"$ref": "#/components/schemas/Level2"}}}, "Level2": {"type": "object", "properties": {"level3": {"$ref": "#/components/schemas/Level3"}}}, "Level3": {"type": "object", "properties": {"value": {"type": "string"}}}}}, "paths": {"/no-operation-id": {"post": {"summary": "Endpoint without operationId", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}, "/circular/{id}": {"get": {"operationId": "getCircular", "summary": "Get circular reference object", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}]}, "post": {"operationId": "createCircular", "summary": "Create circular reference object", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CircularRef"}}}}}}, "/deep-nested": {"post": {"operationId": "createDeepNested", "summary": "Create deeply nested object", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeepNested"}}}}}}, "/mixed-params/{pathParam}": {"post": {"operationId": "mixedParameters", "summary": "Endpoint with mixed parameter types", "parameters": [{"name": "pathParam", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "queryParam", "in": "query", "schema": {"type": "integer"}}, {"name": "X-Custom-Header", "in": "header", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "number"}}}}}}}}