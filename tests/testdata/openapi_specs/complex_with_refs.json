{"openapi": "3.0.0", "info": {"title": "Complex API with References", "version": "1.0.0"}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "string", "description": "User ID"}, "name": {"type": "string", "description": "User's full name"}, "profile": {"$ref": "#/components/schemas/Profile"}, "manager": {"$ref": "#/components/schemas/User"}}, "required": ["id", "name"]}, "Profile": {"type": "object", "properties": {"bio": {"type": "string"}, "avatar": {"type": "string", "format": "uri"}, "preferences": {"$ref": "#/components/schemas/Preferences"}}}, "Preferences": {"type": "object", "properties": {"theme": {"type": "string", "enum": ["light", "dark"]}, "notifications": {"type": "boolean"}}}, "CreateUserRequest": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "profile": {"$ref": "#/components/schemas/Profile"}}, "required": ["name", "email"]}}}, "paths": {"/users/{userId}": {"get": {"operationId": "getUserById", "summary": "Get user by ID", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "expand", "in": "query", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}]}, "put": {"operationId": "updateUser", "summary": "Update user", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}, "/users": {"post": {"operationId": "createUser", "summary": "Create a new user", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}}}}}