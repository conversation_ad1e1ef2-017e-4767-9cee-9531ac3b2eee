package models

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/models"
)

func TestOpenAPISpec_JSONMarshaling(t *testing.T) {
	spec := models.OpenAPISpec{
		Paths: map[string]models.PathItem{
			"/users": {
				"get": models.Operation{
					Summary:     "List users",
					Description: "Get all users",
					OperationID: "listUsers",
					Parameters:  []models.Parameter{},
				},
				"post": models.Operation{
					Summary:     "Create user",
					Description: "Create a new user",
					OperationID: "createUser",
					Parameters:  []models.Parameter{},
				},
			},
		},
		Components: map[string]interface{}{
			"schemas": map[string]interface{}{
				"User": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"id":   map[string]interface{}{"type": "string"},
						"name": map[string]interface{}{"type": "string"},
					},
				},
			},
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(spec)
	require.NoError(t, err)

	// Unmarshal back
	var unmarshaledSpec models.OpenAPISpec
	err = json.Unmarshal(jsonData, &unmarshaledSpec)
	require.NoError(t, err)

	// Verify structure
	assert.Equal(t, spec.Paths, unmarshaledSpec.Paths)
	assert.Equal(t, spec.Components, unmarshaledSpec.Components)
}

func TestOperation_JSONMarshaling(t *testing.T) {
	operation := models.Operation{
		Summary:     "Get user by ID",
		Description: "Retrieve a single user by their unique identifier",
		OperationID: "getUserById",
		Parameters: []models.Parameter{
			{
				Name:        "userId",
				In:          "path",
				Description: "User ID",
				Required:    true,
				Schema: models.SchemaDef{
					"type": "string",
				},
			},
			{
				Name:        "include",
				In:          "query",
				Description: "Include additional fields",
				Required:    false,
				Schema: models.SchemaDef{
					"type": "string",
				},
			},
		},
		RequestBody: models.RequestBody{
			Description: "User data",
			Required:    true,
			Content: map[string]models.MediaType{
				"application/json": {
					Schema: models.SchemaDef{
						"type": "object",
						"properties": map[string]interface{}{
							"name": map[string]interface{}{"type": "string"},
						},
					},
				},
			},
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(operation)
	require.NoError(t, err)

	// Unmarshal back
	var unmarshaledOperation models.Operation
	err = json.Unmarshal(jsonData, &unmarshaledOperation)
	require.NoError(t, err)

	// Verify fields
	assert.Equal(t, operation.Summary, unmarshaledOperation.Summary)
	assert.Equal(t, operation.Description, unmarshaledOperation.Description)
	assert.Equal(t, operation.OperationID, unmarshaledOperation.OperationID)
	assert.Equal(t, operation.Parameters, unmarshaledOperation.Parameters)
	assert.Equal(t, operation.RequestBody, unmarshaledOperation.RequestBody)
}

func TestParameter_JSONMarshaling(t *testing.T) {
	testCases := []struct {
		name      string
		parameter models.Parameter
	}{
		{
			name: "Path parameter",
			parameter: models.Parameter{
				Name:        "userId",
				In:          "path",
				Description: "User ID",
				Required:    true,
				Schema: models.SchemaDef{
					"type": "string",
				},
			},
		},
		{
			name: "Query parameter",
			parameter: models.Parameter{
				Name:        "filter",
				In:          "query",
				Description: "Filter criteria",
				Required:    false,
				Schema: models.SchemaDef{
					"type":   "string",
					"format": "email",
				},
			},
		},
		{
			name: "Header parameter",
			parameter: models.Parameter{
				Name:        "X-API-Key",
				In:          "header",
				Description: "API Key",
				Required:    true,
				Schema: models.SchemaDef{
					"type": "string",
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Marshal to JSON
			jsonData, err := json.Marshal(tc.parameter)
			require.NoError(t, err)

			// Unmarshal back
			var unmarshaledParameter models.Parameter
			err = json.Unmarshal(jsonData, &unmarshaledParameter)
			require.NoError(t, err)

			// Verify fields
			assert.Equal(t, tc.parameter.Name, unmarshaledParameter.Name)
			assert.Equal(t, tc.parameter.In, unmarshaledParameter.In)
			assert.Equal(t, tc.parameter.Description, unmarshaledParameter.Description)
			assert.Equal(t, tc.parameter.Required, unmarshaledParameter.Required)
			assert.Equal(t, tc.parameter.Schema, unmarshaledParameter.Schema)
		})
	}
}

func TestRequestBody_JSONMarshaling(t *testing.T) {
	requestBody := models.RequestBody{
		Description: "User creation data",
		Required:    true,
		Content: map[string]models.MediaType{
			"application/json": {
				Schema: models.SchemaDef{
					"type": "object",
					"properties": map[string]interface{}{
						"name": map[string]interface{}{
							"type":        "string",
							"description": "User's full name",
						},
						"email": map[string]interface{}{
							"type":   "string",
							"format": "email",
						},
					},
					"required": []string{"name", "email"},
				},
			},
			"application/xml": {
				Schema: models.SchemaDef{
					"type": "string",
				},
			},
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(requestBody)
	require.NoError(t, err)

	// Unmarshal back
	var unmarshaledRequestBody models.RequestBody
	err = json.Unmarshal(jsonData, &unmarshaledRequestBody)
	require.NoError(t, err)

	// Verify fields
	assert.Equal(t, requestBody.Description, unmarshaledRequestBody.Description)
	assert.Equal(t, requestBody.Required, unmarshaledRequestBody.Required)
	
	// Compare JSON representations for content since JSON unmarshaling can change types
	originalJSON, _ := json.Marshal(requestBody.Content)
	unmarshaledJSON, _ := json.Marshal(unmarshaledRequestBody.Content)
	assert.JSONEq(t, string(originalJSON), string(unmarshaledJSON))
}

func TestMediaType_JSONMarshaling(t *testing.T) {
	mediaType := models.MediaType{
		Schema: models.SchemaDef{
			"type": "object",
			"properties": map[string]interface{}{
				"data": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"$ref": "#/components/schemas/User",
					},
				},
			},
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(mediaType)
	require.NoError(t, err)

	// Unmarshal back
	var unmarshaledMediaType models.MediaType
	err = json.Unmarshal(jsonData, &unmarshaledMediaType)
	require.NoError(t, err)

	// Verify fields
	assert.Equal(t, mediaType.Schema, unmarshaledMediaType.Schema)
}

func TestSchemaDef_FlexibleTypes(t *testing.T) {
	testCases := []struct {
		name   string
		schema models.SchemaDef
	}{
		{
			name: "Simple string schema",
			schema: models.SchemaDef{
				"type":        "string",
				"description": "A simple string",
			},
		},
		{
			name: "Object with properties",
			schema: models.SchemaDef{
				"type": "object",
				"properties": map[string]interface{}{
					"name": map[string]interface{}{
						"type": "string",
					},
					"age": map[string]interface{}{
						"type":    "integer",
						"minimum": 0,
					},
				},
				"required": []string{"name"},
			},
		},
		{
			name: "Array schema",
			schema: models.SchemaDef{
				"type": "array",
				"items": map[string]interface{}{
					"type": "string",
				},
			},
		},
		{
			name: "Schema with $ref",
			schema: models.SchemaDef{
				"$ref": "#/components/schemas/User",
			},
		},
		{
			name: "Complex nested schema",
			schema: models.SchemaDef{
				"type": "object",
				"properties": map[string]interface{}{
					"user": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"profile": map[string]interface{}{
								"$ref": "#/components/schemas/Profile",
							},
						},
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Marshal to JSON
			jsonData, err := json.Marshal(tc.schema)
			require.NoError(t, err)

			// Unmarshal back
			var unmarshaledSchema models.SchemaDef
			err = json.Unmarshal(jsonData, &unmarshaledSchema)
			require.NoError(t, err)

			// Compare JSON representations since JSON unmarshaling can change types
			originalJSON, _ := json.Marshal(tc.schema)
			unmarshaledJSON, _ := json.Marshal(unmarshaledSchema)
			assert.JSONEq(t, string(originalJSON), string(unmarshaledJSON))
		})
	}
}

func TestPathItem_MultipleOperations(t *testing.T) {
	pathItem := models.PathItem{
		"get": models.Operation{
			Summary:     "Get resource",
			OperationID: "getResource",
		},
		"post": models.Operation{
			Summary:     "Create resource",
			OperationID: "createResource",
		},
		"put": models.Operation{
			Summary:     "Update resource",
			OperationID: "updateResource",
		},
		"delete": models.Operation{
			Summary:     "Delete resource",
			OperationID: "deleteResource",
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(pathItem)
	require.NoError(t, err)

	// Unmarshal back
	var unmarshaledPathItem models.PathItem
	err = json.Unmarshal(jsonData, &unmarshaledPathItem)
	require.NoError(t, err)

	// Verify all operations are preserved
	assert.Equal(t, pathItem, unmarshaledPathItem)
	assert.Len(t, unmarshaledPathItem, 4)
	assert.Contains(t, unmarshaledPathItem, "get")
	assert.Contains(t, unmarshaledPathItem, "post")
	assert.Contains(t, unmarshaledPathItem, "put")
	assert.Contains(t, unmarshaledPathItem, "delete")
}

func TestOpenAPISpec_WithComponents(t *testing.T) {
	spec := models.OpenAPISpec{
		Paths: map[string]models.PathItem{
			"/users/{id}": {
				"get": models.Operation{
					OperationID: "getUser",
					Parameters: []models.Parameter{
						{
							Name:     "id",
							In:       "path",
							Required: true,
							Schema:   models.SchemaDef{"type": "string"},
						},
					},
				},
			},
		},
		Components: map[string]interface{}{
			"schemas": map[string]interface{}{
				"User": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"id": map[string]interface{}{
							"type": "string",
						},
						"name": map[string]interface{}{
							"type": "string",
						},
						"profile": map[string]interface{}{
							"$ref": "#/components/schemas/Profile",
						},
					},
				},
				"Profile": map[string]interface{}{
					"type": "object",
					"properties": map[string]interface{}{
						"bio": map[string]interface{}{
							"type": "string",
						},
					},
				},
			},
			"securitySchemes": map[string]interface{}{
				"bearerAuth": map[string]interface{}{
					"type":   "http",
					"scheme": "bearer",
				},
			},
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(spec)
	require.NoError(t, err)

	// Unmarshal back
	var unmarshaledSpec models.OpenAPISpec
	err = json.Unmarshal(jsonData, &unmarshaledSpec)
	require.NoError(t, err)

	// Verify components are preserved
	assert.Equal(t, spec.Components, unmarshaledSpec.Components)
	
	// Verify specific component access
	schemas, ok := unmarshaledSpec.Components["schemas"].(map[string]interface{})
	require.True(t, ok)
	assert.Contains(t, schemas, "User")
	assert.Contains(t, schemas, "Profile")
}