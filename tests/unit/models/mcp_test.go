package models

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/models"
)

func TestTool_JSONMarshaling(t *testing.T) {
	tool := models.Tool{
		Name:        "testTool",
		Description: "A test tool",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"name": map[string]interface{}{
					"type":        "string",
					"description": "User name",
				},
				"age": map[string]interface{}{
					"type": "integer",
					"minimum": 0,
				},
			},
			"required": []string{"name"},
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(tool)
	require.NoError(t, err)

	// Unmarshal back
	var unmarshaledTool models.Tool
	err = json.Unmarshal(jsonData, &unmarshaledTool)
	require.NoError(t, err)

	// Verify fields
	assert.Equal(t, tool.Name, unmarshaledTool.Name)
	assert.Equal(t, tool.Description, unmarshaledTool.Description)
	
	// For input schema, compare JSON representations since JSON unmarshaling
	// can change types (e.g., []string becomes []interface{})
	originalJSON, _ := json.Marshal(tool.InputSchema)
	unmarshaledJSON, _ := json.Marshal(unmarshaledTool.InputSchema)
	assert.JSONEq(t, string(originalJSON), string(unmarshaledJSON))
}

func TestConversionResult_Structure(t *testing.T) {
	tool := models.Tool{
		Name:        "createUser",
		Description: "Create a new user",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"name": map[string]interface{}{
					"type": "string",
				},
			},
		},
	}

	mapping := models.MCPToolMapping{
		OpenAPIPath:   "/users",
		HTTPMethod:    "POST",
		ParamMappings: map[string]models.ParamMapping{},
		BodyMapping: models.BodyMapping{
			Strategy:         "merged",
			SourceProperties: []string{"name"},
		},
	}

	result := models.ConversionResult{
		Tool:    tool,
		Mapping: mapping,
	}

	// Verify structure
	assert.Equal(t, "createUser", result.Tool.Name)
	assert.Equal(t, "/users", result.Mapping.OpenAPIPath)
	assert.Equal(t, "POST", result.Mapping.HTTPMethod)
	assert.Equal(t, "merged", result.Mapping.BodyMapping.Strategy)
}

func TestMCPToolMapping_JSONMarshaling(t *testing.T) {
	mapping := models.MCPToolMapping{
		OpenAPIPath: "/users/{userId}",
		HTTPMethod:  "GET",
		ParamMappings: map[string]models.ParamMapping{
			"userId": {
				Source:     "path",
				SourceName: "userId",
			},
			"include": {
				Source:     "query",
				SourceName: "include",
			},
		},
		BodyMapping: models.BodyMapping{
			Strategy:        "wrapped",
			WrappedProperty: "body",
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(mapping)
	require.NoError(t, err)

	// Unmarshal back
	var unmarshaledMapping models.MCPToolMapping
	err = json.Unmarshal(jsonData, &unmarshaledMapping)
	require.NoError(t, err)

	// Verify fields
	assert.Equal(t, mapping.OpenAPIPath, unmarshaledMapping.OpenAPIPath)
	assert.Equal(t, mapping.HTTPMethod, unmarshaledMapping.HTTPMethod)
	assert.Equal(t, mapping.ParamMappings, unmarshaledMapping.ParamMappings)
	assert.Equal(t, mapping.BodyMapping, unmarshaledMapping.BodyMapping)
}

func TestParamMapping_JSONMarshaling(t *testing.T) {
	paramMapping := models.ParamMapping{
		Source:     "query",
		SourceName: "filterBy",
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(paramMapping)
	require.NoError(t, err)

	// Unmarshal back
	var unmarshaledParamMapping models.ParamMapping
	err = json.Unmarshal(jsonData, &unmarshaledParamMapping)
	require.NoError(t, err)

	// Verify fields
	assert.Equal(t, paramMapping.Source, unmarshaledParamMapping.Source)
	assert.Equal(t, paramMapping.SourceName, unmarshaledParamMapping.SourceName)
}

func TestBodyMapping_JSONMarshaling(t *testing.T) {
	testCases := []struct {
		name        string
		bodyMapping models.BodyMapping
	}{
		{
			name: "Wrapped strategy",
			bodyMapping: models.BodyMapping{
				Strategy:        "wrapped",
				WrappedProperty: "body",
			},
		},
		{
			name: "Merged strategy",
			bodyMapping: models.BodyMapping{
				Strategy:         "merged",
				SourceProperties: []string{"name", "email", "profile"},
			},
		},
		{
			name: "Empty body mapping",
			bodyMapping: models.BodyMapping{},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Marshal to JSON
			jsonData, err := json.Marshal(tc.bodyMapping)
			require.NoError(t, err)

			// Unmarshal back
			var unmarshaledBodyMapping models.BodyMapping
			err = json.Unmarshal(jsonData, &unmarshaledBodyMapping)
			require.NoError(t, err)

			// Verify fields
			assert.Equal(t, tc.bodyMapping.Strategy, unmarshaledBodyMapping.Strategy)
			assert.Equal(t, tc.bodyMapping.WrappedProperty, unmarshaledBodyMapping.WrappedProperty)
			assert.Equal(t, tc.bodyMapping.SourceProperties, unmarshaledBodyMapping.SourceProperties)
		})
	}
}

func TestBodyMapping_WrappedStrategy(t *testing.T) {
	bodyMapping := models.BodyMapping{
		Strategy:        "wrapped",
		WrappedProperty: "requestData",
	}

	assert.Equal(t, "wrapped", bodyMapping.Strategy)
	assert.Equal(t, "requestData", bodyMapping.WrappedProperty)
	assert.Empty(t, bodyMapping.SourceProperties)
}

func TestBodyMapping_MergedStrategy(t *testing.T) {
	bodyMapping := models.BodyMapping{
		Strategy:         "merged",
		SourceProperties: []string{"name", "email", "preferences"},
	}

	assert.Equal(t, "merged", bodyMapping.Strategy)
	assert.Empty(t, bodyMapping.WrappedProperty)
	assert.Equal(t, []string{"name", "email", "preferences"}, bodyMapping.SourceProperties)
}

func TestParamMapping_AllSources(t *testing.T) {
	testCases := []struct {
		source     string
		sourceName string
	}{
		{"path", "userId"},
		{"query", "filter"},
		{"header", "X-Custom-Header"},
	}

	for _, tc := range testCases {
		t.Run(tc.source, func(t *testing.T) {
			paramMapping := models.ParamMapping{
				Source:     tc.source,
				SourceName: tc.sourceName,
			}

			assert.Equal(t, tc.source, paramMapping.Source)
			assert.Equal(t, tc.sourceName, paramMapping.SourceName)
		})
	}
}

func TestComplexConversionResult_JSONMarshaling(t *testing.T) {
	// Create a complex conversion result with nested structures
	result := models.ConversionResult{
		Tool: models.Tool{
			Name:        "complexOperation",
			Description: "A complex operation with nested schema",
			InputSchema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"user": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"profile": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"preferences": map[string]interface{}{
										"type": "object",
										"properties": map[string]interface{}{
											"theme": map[string]interface{}{
												"type": "string",
												"enum": []string{"light", "dark"},
											},
										},
									},
								},
							},
						},
					},
				},
				"required": []string{"user"},
			},
		},
		Mapping: models.MCPToolMapping{
			OpenAPIPath: "/complex/{id}",
			HTTPMethod:  "PUT",
			ParamMappings: map[string]models.ParamMapping{
				"id": {Source: "path", SourceName: "id"},
				"version": {Source: "header", SourceName: "X-Version"},
			},
			BodyMapping: models.BodyMapping{
				Strategy:         "merged",
				SourceProperties: []string{"user", "metadata"},
			},
		},
	}

	// Marshal to JSON
	jsonData, err := json.Marshal(result)
	require.NoError(t, err)

	// Unmarshal back
	var unmarshaledResult models.ConversionResult
	err = json.Unmarshal(jsonData, &unmarshaledResult)
	require.NoError(t, err)

	// Verify structure is preserved
	assert.Equal(t, result.Tool.Name, unmarshaledResult.Tool.Name)
	assert.Equal(t, result.Tool.Description, unmarshaledResult.Tool.Description)
	assert.Equal(t, result.Mapping.OpenAPIPath, unmarshaledResult.Mapping.OpenAPIPath)
	assert.Equal(t, result.Mapping.HTTPMethod, unmarshaledResult.Mapping.HTTPMethod)
	
	// Verify nested schema is preserved
	inputSchema := unmarshaledResult.Tool.InputSchema
	properties, ok := inputSchema["properties"].(map[string]interface{})
	require.True(t, ok)
	assert.Contains(t, properties, "user")
	
	// Verify param mappings
	assert.Equal(t, result.Mapping.ParamMappings, unmarshaledResult.Mapping.ParamMappings)
	
	// Verify body mapping
	assert.Equal(t, result.Mapping.BodyMapping, unmarshaledResult.Mapping.BodyMapping)
}