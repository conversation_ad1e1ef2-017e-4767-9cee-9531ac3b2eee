package schema

import (
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/models"
	"github.com/ravan/suse-air/internal/schema"
)

func TestBuildRequest_PathParameters(t *testing.T) {
	// Setup test data
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:       1,
		ToolName:     "getUser",
		Description:  pgtype.Text{String: "Get user by ID", Valid: true},
		InputSchema:  []byte(`{"type": "object", "properties": {"userId": {"type": "string"}}}`),
		MappingID:    1,
		OpenapiPath:  "/users/{userId}",
		HttpMethod:   "GET",
		ParamMappings: mustMarshalJSON(map[string]models.ParamMapping{
			"userId": {Source: "path", SourceName: "userId"},
		}),
		BodyMapping: mustMarshalJSON(models.BodyMapping{}),
	}

	mcpArgs := map[string]interface{}{
		"userId": "123",
	}

	baseURL := "https://api.example.com"

	// Build request
	req, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	require.NoError(t, err)

	// Verify request
	assert.Equal(t, "GET", req.Method)
	assert.Equal(t, "https://api.example.com/users/123", req.URL.String())
	assert.Nil(t, req.Body)
}

func TestBuildRequest_QueryParameters(t *testing.T) {
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:       1,
		ToolName:     "getUser",
		Description:  pgtype.Text{String: "Get user by ID", Valid: true},
		InputSchema:  []byte(`{"type": "object"}`),
		MappingID:    1,
		OpenapiPath:  "/users/{userId}",
		HttpMethod:   "GET",
		ParamMappings: mustMarshalJSON(map[string]models.ParamMapping{
			"userId":  {Source: "path", SourceName: "userId"},
			"include": {Source: "query", SourceName: "include"},
			"expand":  {Source: "query", SourceName: "expand"},
		}),
		BodyMapping: mustMarshalJSON(models.BodyMapping{}),
	}

	mcpArgs := map[string]interface{}{
		"userId":  "123",
		"include": "profile",
		"expand":  "metadata",
	}

	baseURL := "https://api.example.com"

	req, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	require.NoError(t, err)

	assert.Equal(t, "GET", req.Method)
	assert.Contains(t, req.URL.String(), "https://api.example.com/users/123")
	
	// Check query parameters
	queryParams := req.URL.Query()
	assert.Equal(t, "profile", queryParams.Get("include"))
	assert.Equal(t, "metadata", queryParams.Get("expand"))
}

func TestBuildRequest_HeaderParameters(t *testing.T) {
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:       1,
		ToolName:     "getUser",
		Description:  pgtype.Text{String: "Get user by ID", Valid: true},
		InputSchema:  []byte(`{"type": "object"}`),
		MappingID:    1,
		OpenapiPath:  "/users/{userId}",
		HttpMethod:   "GET",
		ParamMappings: mustMarshalJSON(map[string]models.ParamMapping{
			"userId":        {Source: "path", SourceName: "userId"},
			"Authorization": {Source: "header", SourceName: "Authorization"},
			"X-Custom-Header": {Source: "header", SourceName: "X-Custom-Header"},
		}),
		BodyMapping: mustMarshalJSON(models.BodyMapping{}),
	}

	mcpArgs := map[string]interface{}{
		"userId":          "123",
		"Authorization":   "Bearer token123",
		"X-Custom-Header": "custom-value",
	}

	baseURL := "https://api.example.com"

	req, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	require.NoError(t, err)

	assert.Equal(t, "GET", req.Method)
	assert.Equal(t, "Bearer token123", req.Header.Get("Authorization"))
	assert.Equal(t, "custom-value", req.Header.Get("X-Custom-Header"))
}

func TestBuildRequest_WrappedBodyStrategy(t *testing.T) {
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:      1,
		ToolName:    "sayHello",
		Description: pgtype.Text{String: "Say hello", Valid: true},
		InputSchema: []byte(`{"type": "object", "properties": {"body": {"type": "string"}}}`),
		MappingID:   1,
		OpenapiPath: "/hello",
		HttpMethod:  "POST",
		ParamMappings: mustMarshalJSON(map[string]models.ParamMapping{}),
		BodyMapping: mustMarshalJSON(models.BodyMapping{
			Strategy:        "wrapped",
			WrappedProperty: "body",
		}),
	}

	mcpArgs := map[string]interface{}{
		"body": "John Doe",
	}

	baseURL := "https://api.example.com"

	req, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	require.NoError(t, err)

	assert.Equal(t, "POST", req.Method)
	assert.Equal(t, "https://api.example.com/hello", req.URL.String())
	assert.Equal(t, "application/json", req.Header.Get("Content-Type"))

	// Read and verify body
	bodyBytes, err := io.ReadAll(req.Body)
	require.NoError(t, err)
	assert.Equal(t, `"John Doe"`, string(bodyBytes))
}

func TestBuildRequest_MergedBodyStrategy(t *testing.T) {
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:      1,
		ToolName:    "createUser",
		Description: pgtype.Text{String: "Create a new user", Valid: true},
		InputSchema: []byte(`{"type": "object"}`),
		MappingID:   1,
		OpenapiPath: "/users",
		HttpMethod:  "POST",
		ParamMappings: mustMarshalJSON(map[string]models.ParamMapping{}),
		BodyMapping: mustMarshalJSON(models.BodyMapping{
			Strategy:         "merged",
			SourceProperties: []string{"name", "email"},
		}),
	}

	mcpArgs := map[string]interface{}{
		"name":  "John Doe",
		"email": "<EMAIL>",
	}

	baseURL := "https://api.example.com"

	req, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	require.NoError(t, err)

	assert.Equal(t, "POST", req.Method)
	assert.Equal(t, "https://api.example.com/users", req.URL.String())
	assert.Equal(t, "application/json", req.Header.Get("Content-Type"))

	// Read and verify body
	bodyBytes, err := io.ReadAll(req.Body)
	require.NoError(t, err)

	var bodyObj map[string]interface{}
	err = json.Unmarshal(bodyBytes, &bodyObj)
	require.NoError(t, err)

	assert.Equal(t, "John Doe", bodyObj["name"])
	assert.Equal(t, "<EMAIL>", bodyObj["email"])
}

func TestBuildRequest_ComplexMixedParameters(t *testing.T) {
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:      1,
		ToolName:    "mixedParameters",
		Description: pgtype.Text{String: "Mixed parameter types", Valid: true},
		InputSchema: []byte(`{"type": "object"}`),
		MappingID:   1,
		OpenapiPath: "/mixed-params/{pathParam}",
		HttpMethod:  "POST",
		ParamMappings: mustMarshalJSON(map[string]models.ParamMapping{
			"pathParam":       {Source: "path", SourceName: "pathParam"},
			"queryParam":      {Source: "query", SourceName: "queryParam"},
			"X-Custom-Header": {Source: "header", SourceName: "X-Custom-Header"},
		}),
		BodyMapping: mustMarshalJSON(models.BodyMapping{
			Strategy:        "wrapped",
			WrappedProperty: "body",
		}),
	}

	mcpArgs := map[string]interface{}{
		"pathParam":       "path-value",
		"queryParam":      42,
		"X-Custom-Header": "header-value",
		"body":            123.45,
	}

	baseURL := "https://api.example.com"

	req, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	require.NoError(t, err)

	// Verify URL and path
	assert.Equal(t, "POST", req.Method)
	assert.Contains(t, req.URL.String(), "https://api.example.com/mixed-params/path-value")

	// Verify query parameters
	queryParams := req.URL.Query()
	assert.Equal(t, "42", queryParams.Get("queryParam"))

	// Verify headers
	assert.Equal(t, "header-value", req.Header.Get("X-Custom-Header"))
	assert.Equal(t, "application/json", req.Header.Get("Content-Type"))

	// Verify body
	bodyBytes, err := io.ReadAll(req.Body)
	require.NoError(t, err)
	assert.Equal(t, "123.45", string(bodyBytes))
}

func TestBuildRequest_NoBody(t *testing.T) {
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:        1,
		ToolName:      "getUser",
		Description:   pgtype.Text{String: "Get user", Valid: true},
		InputSchema:   []byte(`{"type": "object"}`),
		MappingID:     1,
		OpenapiPath:   "/users/{userId}",
		HttpMethod:    "GET",
		ParamMappings: mustMarshalJSON(map[string]models.ParamMapping{
			"userId": {Source: "path", SourceName: "userId"},
		}),
		BodyMapping: mustMarshalJSON(models.BodyMapping{}), // No body strategy
	}

	mcpArgs := map[string]interface{}{
		"userId": "123",
	}

	baseURL := "https://api.example.com"

	req, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	require.NoError(t, err)

	assert.Equal(t, "GET", req.Method)
	assert.Equal(t, "https://api.example.com/users/123", req.URL.String())
	assert.Nil(t, req.Body)
	assert.Empty(t, req.Header.Get("Content-Type"))
}

func TestBuildRequest_BaseURLHandling(t *testing.T) {
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:        1,
		ToolName:      "getUser",
		Description:   pgtype.Text{String: "Get user", Valid: true},
		InputSchema:   []byte(`{"type": "object"}`),
		MappingID:     1,
		OpenapiPath:   "/users",
		HttpMethod:    "GET",
		ParamMappings: mustMarshalJSON(map[string]models.ParamMapping{}),
		BodyMapping:   mustMarshalJSON(models.BodyMapping{}),
	}

	mcpArgs := map[string]interface{}{}

	testCases := []struct {
		name     string
		baseURL  string
		expected string
	}{
		{
			name:     "Base URL with trailing slash",
			baseURL:  "https://api.example.com/",
			expected: "https://api.example.com/users",
		},
		{
			name:     "Base URL without trailing slash",
			baseURL:  "https://api.example.com",
			expected: "https://api.example.com/users",
		},
		{
			name:     "Base URL with path",
			baseURL:  "https://api.example.com/v1/",
			expected: "https://api.example.com/v1/users",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			req, err := schema.BuildRequest(toolWithMapping, mcpArgs, tc.baseURL)
			require.NoError(t, err)
			assert.Equal(t, tc.expected, req.URL.String())
		})
	}
}

func TestBuildRequest_InvalidMappingData(t *testing.T) {
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:        1,
		ToolName:      "test",
		Description:   pgtype.Text{String: "Test", Valid: true},
		InputSchema:   []byte(`{"type": "object"}`),
		MappingID:     1,
		OpenapiPath:   "/test",
		HttpMethod:    "GET",
		ParamMappings: []byte(`invalid json`), // Invalid JSON
		BodyMapping:   mustMarshalJSON(models.BodyMapping{}),
	}

	mcpArgs := map[string]interface{}{}
	baseURL := "https://api.example.com"

	_, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "could not unmarshal param mappings")
}

func TestBuildRequest_InvalidBodyMappingData(t *testing.T) {
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:        1,
		ToolName:      "test",
		Description:   pgtype.Text{String: "Test", Valid: true},
		InputSchema:   []byte(`{"type": "object"}`),
		MappingID:     1,
		OpenapiPath:   "/test",
		HttpMethod:    "GET",
		ParamMappings: mustMarshalJSON(map[string]models.ParamMapping{}),
		BodyMapping:   []byte(`invalid json`), // Invalid JSON
	}

	mcpArgs := map[string]interface{}{}
	baseURL := "https://api.example.com"

	_, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "could not unmarshal body mapping")
}

func TestBuildRequest_InvalidURL(t *testing.T) {
	toolWithMapping := db.GetToolWithMappingRow{
		ToolID:        1,
		ToolName:      "test",
		Description:   pgtype.Text{String: "Test", Valid: true},
		InputSchema:   []byte(`{"type": "object"}`),
		MappingID:     1,
		OpenapiPath:   "/test",
		HttpMethod:    "GET",
		ParamMappings: mustMarshalJSON(map[string]models.ParamMapping{}),
		BodyMapping:   mustMarshalJSON(models.BodyMapping{}),
	}

	mcpArgs := map[string]interface{}{}
	baseURL := "://invalid-url" // Invalid URL

	_, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to parse URL")
}

func TestAPIClient_NewAPIClient(t *testing.T) {
	client := schema.NewAPIClient()
	assert.NotNil(t, client)
}

func TestAPIClient_Do(t *testing.T) {
	client := schema.NewAPIClient()
	
	// Create a simple request
	req, err := http.NewRequest("GET", "https://httpbin.org/get", nil)
	require.NoError(t, err)
	
	// Note: This is a real HTTP call, but httpbin.org is a testing service
	// In a real test environment, you might want to mock this
	resp, err := client.Do(req)
	if err != nil {
		t.Skip("Skipping test due to network error (expected in CI environments)")
	}
	defer resp.Body.Close()
	
	assert.Equal(t, http.StatusOK, resp.StatusCode)
}

// Helper function to marshal JSON and panic on error (for test setup)
func mustMarshalJSON(v interface{}) []byte {
	data, err := json.Marshal(v)
	if err != nil {
		panic(err)
	}
	return data
}