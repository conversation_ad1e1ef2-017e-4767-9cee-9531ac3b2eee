package unit_test

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"testing"

	"github.com/ravan/suse-air/internal/cmd"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/tests/unit/test_utils"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/urfave/cli/v2"
)

func TestRunToolAssociate(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)

	profileName := "test-profile"
	toolName := "test-tool"
	acl := "read"

	profile := db.Profile{ID: 1, Name: profileName}
	tool := db.McpTool{ID: 10, Name: toolName}
	profileTool := db.ProfileTool{ProfileID: profile.ID, ToolID: tool.ID, Acl: acl}

	mockQueries.On("GetProfileByName", mock.Anything, profileName).Return(profile, nil).Once()
	mockQueries.On("GetMCPToolByName", mock.Anything, toolName).Return(tool, nil).Once()
	mockQueries.On("CreateProfileTool", mock.Anything, db.CreateProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool.ID,
		Acl:       acl,
	}).Return(profileTool, nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := test_utils.CreateTestContext([]string{"tool", "associate", "--profile", profileName, "--tool", toolName, "--acl", acl}, mockQueries)
	err := cmd.RunToolAssociate(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), fmt.Sprintf("Tool %s associated with profile %s successfully.", toolName, profileName))
	mockQueries.AssertExpectations(t)
}

func TestRunToolDisassociate(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)

	profileName := "test-profile"
	toolName := "test-tool"

	profile := db.Profile{ID: 1, Name: profileName}
	tool := db.McpTool{ID: 10, Name: toolName}

	mockQueries.On("GetProfileByName", mock.Anything, profileName).Return(profile, nil).Once()
	mockQueries.On("GetMCPToolByName", mock.Anything, toolName).Return(tool, nil).Once()
	mockQueries.On("DeleteProfileTool", mock.Anything, db.DeleteProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool.ID,
	}).Return(nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := test_utils.CreateTestContext([]string{"tool", "disassociate", "--profile", profileName, "--tool", toolName}, mockQueries)
	err := cmd.RunToolDisassociate(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), fmt.Sprintf("Tool %s disassociated from profile %s successfully.", toolName, profileName))
	mockQueries.AssertExpectations(t)
}

func TestRunToolList(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)

	profileName := "test-profile"
	expectedTools := []db.McpTool{
		{ID: 1, Name: "tool1", Description: pgtype.Text{String: "Desc1", Valid: true}},
		{ID: 2, Name: "tool2", Description: pgtype.Text{String: "Desc2", Valid: true}},
	}

	mockQueries.On("ListMCPToolsByProfile", mock.Anything, profileName).Return(expectedTools, nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := test_utils.CreateTestContext([]string{"tool", "list", "--profile", profileName}, mockQueries)
	err := cmd.RunToolList(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), "Tools for profile test-profile:")
	assert.Contains(t, string(out), "ID: 1, Name: tool1, Description: Desc1")
	assert.Contains(t, string(out), "ID: 2, Name: tool2, Description: Desc2")
	mockQueries.AssertExpectations(t)
}
