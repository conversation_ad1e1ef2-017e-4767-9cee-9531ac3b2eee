package server

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/server"
)

func TestAuthMiddleware_AudienceValidation(t *testing.T) {
	// Test handler that just returns 200 OK
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	// Wrap with auth middleware
	authHandler := server.AuthMiddleware(testHandler)

	tests := []struct {
		name           string
		profile        string
		host           string
		tokenAudience  string
		expectedStatus int
		description    string
	}{
		{
			name:           "valid_audience_matches_profile",
			profile:        "test-profile",
			host:           "localhost:8080",
			tokenAudience:  "localhost:8080/mcp/test-profile",
			expectedStatus: http.StatusOK,
			description:    "Token with correct audience should be accepted",
		},
		{
			name:           "invalid_audience_wrong_profile",
			profile:        "test-profile",
			host:           "localhost:8080",
			tokenAudience:  "localhost:8080/mcp/wrong-profile",
			expectedStatus: http.StatusUnauthorized,
			description:    "Token with wrong profile in audience should be rejected",
		},
		{
			name:           "invalid_audience_wrong_host",
			profile:        "test-profile",
			host:           "localhost:8080",
			tokenAudience:  "evil.com:8080/mcp/test-profile",
			expectedStatus: http.StatusUnauthorized,
			description:    "Token with wrong host in audience should be rejected",
		},
		{
			name:           "invalid_audience_missing_mcp_path",
			profile:        "test-profile",
			host:           "localhost:8080",
			tokenAudience:  "localhost:8080",
			expectedStatus: http.StatusUnauthorized,
			description:    "Token with incomplete audience path should be rejected",
		},
		{
			name:           "invalid_audience_extra_path",
			profile:        "test-profile",
			host:           "localhost:8080",
			tokenAudience:  "localhost:8080/mcp/test-profile/extra",
			expectedStatus: http.StatusUnauthorized,
			description:    "Token with extra path in audience should be rejected",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a token with the specified audience
			token := createTestTokenWithAudience(t, tt.tokenAudience)

			// Create request with the token
			req := httptest.NewRequest("POST", "/mcp/"+tt.profile, nil)
			req.Host = tt.host
			req.Header.Set("Authorization", "Bearer "+token)

			// Add profile to context (simulating ProfileRouter)
			ctx := context.WithValue(req.Context(), "profile", tt.profile)
			req = req.WithContext(ctx)

			// Record response
			w := httptest.NewRecorder()

			// Execute request
			authHandler.ServeHTTP(w, req)

			// Verify response
			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			if tt.expectedStatus == http.StatusOK {
				assert.Equal(t, "success", w.Body.String())
			}
		})
	}
}

func TestAuthMiddleware_MissingProfile(t *testing.T) {
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	authHandler := server.AuthMiddleware(testHandler)

	// Create a valid token
	token := createTestTokenWithAudience(t, "localhost:8080/mcp/test-profile")

	// Create request without profile in context
	req := httptest.NewRequest("POST", "/mcp/test-profile", nil)
	req.Host = "localhost:8080"
	req.Header.Set("Authorization", "Bearer "+token)
	// Note: NOT adding profile to context

	w := httptest.NewRecorder()
	authHandler.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code, "Request without profile in context should be rejected")
}

func TestAuthMiddleware_InvalidToken(t *testing.T) {
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	authHandler := server.AuthMiddleware(testHandler)

	tests := []struct {
		name           string
		authHeader     string
		expectedStatus int
		description    string
	}{
		{
			name:           "missing_authorization_header",
			authHeader:     "",
			expectedStatus: http.StatusUnauthorized,
			description:    "Request without Authorization header should be rejected",
		},
		{
			name:           "invalid_bearer_format",
			authHeader:     "InvalidFormat token",
			expectedStatus: http.StatusUnauthorized,
			description:    "Request with invalid Authorization format should be rejected",
		},
		{
			name:           "invalid_token",
			authHeader:     "Bearer invalid.jwt.token",
			expectedStatus: http.StatusBadRequest, // JWT parsing error returns 400
			description:    "Request with invalid JWT token should be rejected",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := httptest.NewRequest("POST", "/mcp/test-profile", nil)
			req.Host = "localhost:8080"
			if tt.authHeader != "" {
				req.Header.Set("Authorization", tt.authHeader)
			}

			ctx := context.WithValue(req.Context(), "profile", "test-profile")
			req = req.WithContext(ctx)

			w := httptest.NewRecorder()
			authHandler.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)
		})
	}
}

// Helper function to create a test token with specific audience
func createTestTokenWithAudience(t *testing.T, audience string) string {
	claims := &server.Claims{
		Audience: audience,
		RegisteredClaims: jwt.RegisteredClaims{
			Subject:   "test-user",
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(5 * time.Minute)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "test-issuer",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	
	// Use the same key as the auth middleware
	jwtKey := []byte("your_secret_key") // This should match the key in auth.go
	tokenString, err := token.SignedString(jwtKey)
	require.NoError(t, err)
	
	return tokenString
}
