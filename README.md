# SUSE AI Runtime (AIR)

This project aims to provide a runtime environment for AI models, focusing on the Model Context Protocol (MCP).

## Getting Started

### Prerequisites

- Go (1.22 or later)
- PostgreSQL

### Setup

1.  **Clone the repository:**

    ```bash
    git clone https://github.com/ravan/suse-air.git
    cd suse-air
    ```

2.  **Database Setup:**

    Ensure you have a PostgreSQL instance running. You can use Docker:

    ```bash
    docker run --name some-postgres -e POSTGRES_PASSWORD=devpass -p 5432:5432 -d postgres
    ```

    Run database migrations:

    ```bash
    go run github.com/pressly/goose/v3/cmd/goose postgres "user=devuser password=devpass host=localhost port=5432 dbname=sairdev sslmode=disable" up
    ```

### Building the Project

```bash
go build ./cmd/air
```

## Usage

The `air` command-line tool provides the following subcommands:

### `air convert`

Converts an OpenAPI v3 JSON file to MCP tools and stores them in the database.

```bash
./air convert --file <path_to_openapi_json> [--db <db_connection_string>]
```

- `--file`, `-f`: Path to the OpenAPI v3 JSON file to convert (required).
- `--db`: PostgreSQL connection string (defaults to `postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable`).

### `air execute`

Executes a stored MCP tool.

```bash
./air execute --tool <tool_name> --base-url <api_base_url> [--args <json_args>] [--db <db_connection_string>]
```

- `--tool`, `-t`: The name of the MCP tool to execute (required).
- `--base-url`, `-b`: The base URL of the target API server (e.g., `https://api.example.com`) (required).
- `--args`, `-a`: A JSON string of arguments for the tool (defaults to `{}`).
- `--db`: PostgreSQL connection string (defaults to `postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable`).

### `air serve`

Starts the MCP server.

```bash
./air serve [--port <port_number>] [--db <db_connection_string>]
```

- `--port`, `-p`: Port for the MCP server to listen on (defaults to `8080`).
- `--db`: PostgreSQL connection string (defaults to `postgres://devuser:devpass@localhost:5432/sairdev?sslmode=disable`).
